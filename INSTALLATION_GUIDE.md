# SelexIQ Resources System - Installation Guide

## 🚀 Quick Setup for Shared Hosting

### Step 1: Create Database
1. **Login to your hosting control panel** (cPanel, Plesk, etc.)
2. **Go to MySQL Databases** or **Database Manager**
3. **Create a new database** with any name you prefer (e.g., `your_username_selexiq`)
4. **Create a database user** with full privileges to this database
5. **Note down the database details:**
   - Database Host (usually `localhost`)
   - Database Name
   - Database Username
   - Database Password

### Step 2: Upload Files
1. **Upload all project files** to your web hosting directory
2. **Ensure the file structure** looks like this:
   ```
   public_html/ (or your web root)
   ├── admin/
   ├── config/
   ├── css/
   ├── js/
   ├── images/
   ├── database/
   ├── index.html
   ├── about.html
   ├── resources.php
   └── other files...
   ```

### Step 3: Import Database
1. **Go to phpMyAdmin** in your hosting control panel
2. **Select your database** from the left sidebar
3. **Click on "Import" tab**
4. **Choose file**: Select `database/selexiq_resources.sql`
5. **Click "Go"** to import the database structure and sample data

### Step 4: Configure Database Connection
1. **Open** `config/database.php` in a text editor
2. **Update the database credentials:**
   ```php
   define('DB_HOST', 'localhost');           // Your database host
   define('DB_NAME', 'your_database_name');  // Your database name
   define('DB_USER', 'your_db_username');    // Your database username
   define('DB_PASS', 'your_db_password');    // Your database password
   ```
3. **Save the file**

### Step 5: Test the Installation
1. **Visit your website**: `https://yourdomain.com`
2. **Test the Resources page**: `https://yourdomain.com/resources.php`
3. **Access the Admin Panel**: `https://yourdomain.com/admin/`
4. **Login with default credentials:**
   - Username: `admin`
   - Password: `admin123`

## 🔧 Troubleshooting Common Issues

### Database Import Errors

#### Error: "Access denied to create database"
- **Solution**: Don't create the database via SQL. Create it through your hosting control panel first.

#### Error: "Table already exists"
- **Solution**: The SQL file now includes DROP TABLE statements. If you still get this error, manually delete the tables first.

#### Error: "Foreign key constraint fails"
- **Solution**: The SQL file disables foreign key checks during import. If you still get this error, import the tables in this order:
  1. admin_users
  2. courses
  3. subjects
  4. resources

### File Permission Issues
- **Ensure PHP files have 644 permissions**
- **Ensure directories have 755 permissions**
- **The web server must have read access to all files**

### Database Connection Issues
- **Check your database credentials** in `config/database.php`
- **Verify the database host** (some hosts use different hosts for databases)
- **Ensure the database user has full privileges** to the database

## 🔒 Security Recommendations

### After Installation:
1. **Change the default admin password** immediately
2. **Update the admin email** in the settings
3. **Remove or secure the database folder** from web access
4. **Consider adding SSL certificate** for HTTPS

### Optional Security Enhancements:
1. **Rename the admin folder** to something less obvious
2. **Add IP restrictions** to the admin area
3. **Enable two-factor authentication** (custom implementation)

## 📊 Sample Data Included

The database comes with:
- **7 Courses**: AP, MYP, IGCSE, GCSE, AS Level, A Level, IBDP
- **25+ Subjects** across all courses
- **Sample Resources** with Google Drive links
- **1 Admin User** (admin/admin123)

## 🎨 Customization

### Changing Colors:
Edit `css/styles.css` and update the CSS variables:
```css
:root {
    --primary-color: #182B5C;    /* Your primary color */
    --accent-color: #FFBC00;     /* Your accent color */
}
```

### Adding Your Logo:
Replace `images/logo.png` with your logo (recommended size: 200x60px)

### Customizing Content:
- **Courses**: Add/edit through Admin Panel → Courses
- **Subjects**: Add/edit through Admin Panel → Subjects  
- **Resources**: Add/edit through Admin Panel → Resources

## 📞 Support

If you encounter any issues:
1. **Check the troubleshooting section** above
2. **Verify your hosting requirements** (PHP 7.4+, MySQL 5.7+)
3. **Contact your hosting provider** for database-related issues
4. **Check error logs** in your hosting control panel

## ✅ Success Checklist

- [ ] Database created through hosting control panel
- [ ] All files uploaded to web directory
- [ ] Database imported successfully
- [ ] Database credentials updated in config/database.php
- [ ] Website loads without errors
- [ ] Resources page works correctly
- [ ] Admin panel accessible
- [ ] Can login with admin/admin123
- [ ] Default password changed

---

**🎉 Congratulations!** Your SelexIQ Resources System is now ready to use!
