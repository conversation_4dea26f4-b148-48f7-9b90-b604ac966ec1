<?php
/**
 * SelexIQ Resources Page
 * Dynamic resources management system for courses, subjects, and materials
 */

require_once 'config/database.php';

// Get all active courses
$courses = $db->fetchAll("
    SELECT id, name, description, icon, color 
    FROM courses 
    WHERE status = 'active' 
    ORDER BY sort_order ASC, name ASC
");

// Handle AJAX requests
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    switch ($_GET['action']) {
        case 'get_subjects':
            $course_id = (int)$_GET['course_id'];
            $subjects = $db->fetchAll("
                SELECT id, name, description, icon 
                FROM subjects 
                WHERE course_id = ? AND status = 'active' 
                ORDER BY sort_order ASC, name ASC
            ", [$course_id]);
            echo json_encode($subjects);
            exit;
            
        case 'get_years_months':
            $subject_id = (int)$_GET['subject_id'];
            $years_months = $db->fetchAll("
                SELECT DISTINCT year, month 
                FROM resources 
                WHERE subject_id = ? AND status = 'active' 
                ORDER BY year DESC, month DESC
            ", [$subject_id]);
            echo json_encode($years_months);
            exit;
            
        case 'get_resources':
            $subject_id = (int)$_GET['subject_id'];
            $year = (int)$_GET['year'];
            $month = (int)$_GET['month'];
            
            $resources = $db->fetchAll("
                SELECT id, title, description, google_drive_link, file_type, file_size, download_count
                FROM resources 
                WHERE subject_id = ? AND year = ? AND month = ? AND status = 'active' 
                ORDER BY title ASC
            ", [$subject_id, $year, $month]);
            
            echo json_encode($resources);
            exit;
            
        case 'track_download':
            $resource_id = (int)$_GET['resource_id'];
            $db->execute("UPDATE resources SET download_count = download_count + 1 WHERE id = ?", [$resource_id]);
            echo json_encode(['success' => true]);
            exit;
    }
}

$page_title = "Resources";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - SelexIQ</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/resources.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <link rel="icon" href="images/logo.png" type="image/png">
</head>
<body>
    <!-- Header Section -->
    <header>
        <div class="container">
            <div class="logo">
                <a href="index.html"><img src="images/logo.png" alt="SelexIQ Logo"></a>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="index.html#services">Services</a></li>
                    <li><a href="index.html#programs">Programs</a></li>
                    <li><a href="resources.php" class="active">Resources</a></li>
                    <li><a href="index.html#contact">Contact</a></li>
                </ul>
            </nav>
            <div class="mobile-menu-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </header>

    <!-- Page Banner -->
    <section class="page-banner resources-banner">
        <div class="container">
            <h1 data-aos="fade-up">Educational Resources</h1>
            <p data-aos="fade-up" data-aos-delay="200">Access comprehensive study materials, past papers, and resources for all our courses</p>
        </div>
    </section>

    <!-- Resources Section -->
    <section class="resources-section">
        <div class="container">
            <!-- Breadcrumb Navigation -->
            <div class="breadcrumb" id="breadcrumb">
                <span class="breadcrumb-item active" data-step="courses">
                    <i class="fas fa-graduation-cap"></i> Courses
                </span>
                <span class="breadcrumb-separator">></span>
                <span class="breadcrumb-item" data-step="subjects">
                    <i class="fas fa-book"></i> <span id="breadcrumb-subject">Subjects</span>
                </span>
                <span class="breadcrumb-separator">></span>
                <span class="breadcrumb-item" data-step="timeline">
                    <i class="fas fa-calendar"></i> <span id="breadcrumb-timeline">Timeline</span>
                </span>
                <span class="breadcrumb-separator">></span>
                <span class="breadcrumb-item" data-step="resources">
                    <i class="fas fa-download"></i> <span id="breadcrumb-resources">Resources</span>
                </span>
            </div>

            <!-- Loading Spinner -->
            <div class="loading-spinner" id="loadingSpinner" style="display: none;">
                <div class="spinner"></div>
                <p>Loading...</p>
            </div>

            <!-- Step 1: Courses -->
            <div class="resources-step active" id="step-courses">
                <h2 class="step-title" data-aos="fade-up">
                    <i class="fas fa-graduation-cap"></i>
                    Choose Your Course
                </h2>
                <div class="courses-grid">
                    <?php foreach ($courses as $index => $course): ?>
                    <div class="course-card" 
                         data-course-id="<?php echo $course['id']; ?>"
                         data-course-name="<?php echo htmlspecialchars($course['name']); ?>"
                         data-aos="zoom-in" 
                         data-aos-delay="<?php echo $index * 100; ?>">
                        <div class="course-icon" style="color: <?php echo $course['color']; ?>">
                            <i class="<?php echo $course['icon']; ?>"></i>
                        </div>
                        <h3><?php echo htmlspecialchars($course['name']); ?></h3>
                        <p><?php echo htmlspecialchars($course['description']); ?></p>
                        <div class="course-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Step 2: Subjects -->
            <div class="resources-step" id="step-subjects">
                <div class="step-header">
                    <button class="back-btn" onclick="goToStep('courses')">
                        <i class="fas fa-arrow-left"></i> Back to Courses
                    </button>
                    <h2 class="step-title">
                        <i class="fas fa-book"></i>
                        Choose Subject for <span id="selected-course-name"></span>
                    </h2>
                </div>
                <div class="subjects-grid" id="subjects-container">
                    <!-- Subjects will be loaded dynamically -->
                </div>
            </div>

            <!-- Step 3: Timeline -->
            <div class="resources-step" id="step-timeline">
                <div class="step-header">
                    <button class="back-btn" onclick="goToStep('subjects')">
                        <i class="fas fa-arrow-left"></i> Back to Subjects
                    </button>
                    <h2 class="step-title">
                        <i class="fas fa-calendar"></i>
                        Select Year & Month for <span id="selected-subject-name"></span>
                    </h2>
                </div>
                <div class="timeline-container" id="timeline-container">
                    <!-- Timeline will be loaded dynamically -->
                </div>
            </div>

            <!-- Step 4: Resources -->
            <div class="resources-step" id="step-resources">
                <div class="step-header">
                    <button class="back-btn" onclick="goToStep('timeline')">
                        <i class="fas fa-arrow-left"></i> Back to Timeline
                    </button>
                    <h2 class="step-title">
                        <i class="fas fa-download"></i>
                        Download Resources
                    </h2>
                    <div class="selected-info">
                        <span class="info-item">
                            <i class="fas fa-graduation-cap"></i>
                            <span id="final-course-name"></span>
                        </span>
                        <span class="info-item">
                            <i class="fas fa-book"></i>
                            <span id="final-subject-name"></span>
                        </span>
                        <span class="info-item">
                            <i class="fas fa-calendar"></i>
                            <span id="final-timeline"></span>
                        </span>
                    </div>
                </div>
                <div class="resources-list" id="resources-container">
                    <!-- Resources will be loaded dynamically -->
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo" data-aos="fade-up">
                    <img src="images/logo.png" alt="SelexIQ Logo" class="logo-pulse">
                </div>
                <div class="footer-links" data-aos="fade-up" data-aos-delay="100">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html" class="footer-link-animation">Home</a></li>
                        <li><a href="about.html" class="footer-link-animation">About</a></li>
                        <li><a href="index.html#services" class="footer-link-animation">Services</a></li>
                        <li><a href="index.html#programs" class="footer-link-animation">Programs</a></li>
                        <li><a href="resources.php" class="footer-link-animation">Resources</a></li>
                        <li><a href="index.html#contact" class="footer-link-animation">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-contact" data-aos="fade-up" data-aos-delay="200">
                    <h3>Contact Us</h3>
                    <p class="contact-item-animation"><i class="fas fa-map-marker-alt"></i> Dubai, UAE</p>
                    <p class="contact-item-animation"><i class="fas fa-phone"></i> +971 XXXX XXXX</p>
                    <p class="contact-item-animation"><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
                <div class="footer-social" data-aos="fade-up" data-aos-delay="300">
                    <h3>Follow Us</h3>
                    <div class="social-icons">
                        <a href="#" class="social-icon-animation"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon-animation"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon-animation"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-icon-animation"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom" data-aos="fade-up" data-aos-delay="400">
                <p>&copy; 2024 SelexIQ Education Support Services. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script src="js/script.js"></script>
    <script src="js/resources.js"></script>
</body>
</html>
