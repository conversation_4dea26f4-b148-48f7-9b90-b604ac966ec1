/* Analytics Page Styles */

/* Analytics Overview */
.analytics-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.overview-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 20px;
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);
}

.overview-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.overview-icon {
    width: 70px;
    height: 70px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    flex-shrink: 0;
}

.overview-icon.courses {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.overview-icon.subjects {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.overview-icon.resources {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.overview-icon.downloads {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.overview-content h3 {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 5px;
    line-height: 1;
}

.overview-content p {
    color: #666;
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.overview-content small {
    color: #999;
    font-size: 0.9rem;
}

/* Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.chart-container {
    height: 400px;
}

.chart-container .dashboard-section {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.chart-container canvas {
    flex: 1;
    max-height: 300px;
}

/* Popular Resources */
.popular-resources {
    display: grid;
    gap: 15px;
}

.popular-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.popular-item:hover {
    background: white;
    border-color: var(--accent-color);
    transform: translateX(5px);
}

.rank {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.popular-item:nth-child(1) .rank {
    background: linear-gradient(135deg, #FFD700, #FFA500);
}

.popular-item:nth-child(2) .rank {
    background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
}

.popular-item:nth-child(3) .rank {
    background: linear-gradient(135deg, #CD7F32, #B8860B);
}

.resource-info {
    flex: 1;
}

.resource-info h4 {
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.resource-info p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.separator {
    margin: 0 8px;
    color: #ccc;
}

.course {
    font-weight: 600;
}

.subject {
    color: var(--accent-color);
    font-weight: 500;
}

.date {
    color: #999;
}

.download-count {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.download-count i {
    color: var(--accent-color);
}

/* File Types Grid */
.file-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.file-type-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.file-type-card:hover {
    background: white;
    border-color: var(--accent-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.file-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto 15px;
    transition: all 0.3s ease;
}

.file-type-card:hover .file-icon {
    background: var(--accent-color);
    color: var(--primary-color);
}

.file-type-card h4 {
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.file-type-card p {
    color: #666;
    font-weight: 500;
    margin-bottom: 5px;
}

.file-type-card small {
    color: #999;
    font-size: 0.85rem;
}

/* Recent Activity */
.recent-activity {
    display: grid;
    gap: 15px;
}

.activity-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    border-left: 4px solid var(--accent-color);
}

.activity-item:hover {
    background: white;
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: var(--accent-color);
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    color: var(--primary-color);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.activity-content p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.activity-time {
    color: #999;
    font-size: 0.85rem;
    font-weight: 500;
    flex-shrink: 0;
}

/* No Data State */
.no-data {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-data i {
    font-size: 4rem;
    color: var(--accent-color);
    margin-bottom: 20px;
    opacity: 0.7;
}

.no-data h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.no-data p {
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .analytics-overview {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .overview-card {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .overview-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .overview-content h3 {
        font-size: 2rem;
    }
    
    .charts-section {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .chart-container {
        height: 350px;
    }
    
    .popular-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .resource-info {
        order: 1;
    }
    
    .rank {
        order: 2;
    }
    
    .download-count {
        order: 3;
        justify-content: center;
    }
    
    .file-types-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .activity-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .activity-content {
        order: 1;
    }
    
    .activity-icon {
        order: 2;
    }
    
    .activity-time {
        order: 3;
    }
}

@media (max-width: 480px) {
    .overview-card {
        padding: 15px;
    }
    
    .overview-content h3 {
        font-size: 1.8rem;
    }
    
    .chart-container {
        height: 300px;
    }
    
    .popular-item,
    .activity-item {
        padding: 15px;
    }
    
    .file-types-grid {
        grid-template-columns: 1fr;
    }
    
    .file-type-card {
        padding: 20px 15px;
    }
}
