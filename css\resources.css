/* SelexIQ Resources Page Styles */

/* Page Banner */
.resources-banner {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--light-text);
    padding: 100px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.resources-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.resources-banner h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.resources-banner p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

/* Resources Section */
.resources-section {
    padding: 80px 0;
    min-height: 70vh;
}

/* Breadcrumb Navigation */
.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 50px;
    padding: 20px;
    background: rgba(24, 43, 92, 0.05);
    border-radius: 15px;
    gap: 10px;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
    color: #666;
    background: #f8f9fa;
}

.breadcrumb-item.active {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.breadcrumb-item.completed {
    background: var(--accent-color);
    color: var(--primary-color);
}

.breadcrumb-separator {
    color: #ccc;
    font-weight: bold;
    margin: 0 5px;
}

/* Loading Spinner */
.loading-spinner {
    text-align: center;
    padding: 60px 20px;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Resources Steps */
.resources-step {
    display: none;
    animation: fadeInUp 0.6s ease-out;
}

.resources-step.active {
    display: block;
}

.step-header {
    margin-bottom: 40px;
}

.back-btn {
    background: var(--accent-color);
    color: var(--primary-color);
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.back-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(-5px);
}

.step-title {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.step-title i {
    color: var(--accent-color);
}

/* Courses Grid */
.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.course-card {
    background: white;
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.course-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.course-card:hover::before {
    transform: scaleX(1);
}

.course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--accent-color);
}

.course-icon {
    font-size: 4rem;
    margin-bottom: 25px;
    transition: all 0.3s ease;
}

.course-card:hover .course-icon {
    transform: scale(1.1);
}

.course-card h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.course-card p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 25px;
}

.course-arrow {
    color: var(--accent-color);
    font-size: 1.2rem;
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
}

.course-card:hover .course-arrow {
    opacity: 1;
    transform: translateX(0);
}

/* Subjects Grid */
.subjects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.subject-card {
    background: white;
    border-radius: 15px;
    padding: 30px 25px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.subject-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
    border-color: var(--accent-color);
}

.subject-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.subject-card h4 {
    color: var(--primary-color);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 12px;
}

.subject-card p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Timeline Container */
.timeline-container {
    max-width: 800px;
    margin: 0 auto;
}

.timeline-years {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 40px;
}

.year-btn {
    background: white;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.year-btn:hover,
.year-btn.active {
    background: var(--primary-color);
    color: white;
}

.timeline-months {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-top: 30px;
}

.month-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.month-card:hover {
    border-color: var(--accent-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.month-card.active {
    background: var(--accent-color);
    border-color: var(--accent-color);
    color: var(--primary-color);
}

.month-name {
    font-weight: 600;
    font-size: 1.1rem;
}

/* Selected Info */
.selected-info {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    margin: 20px 0;
    padding: 20px;
    background: rgba(255, 188, 0, 0.1);
    border-radius: 15px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
    font-weight: 600;
}

.info-item i {
    color: var(--accent-color);
}

/* Resources List */
.resources-list {
    display: grid;
    gap: 20px;
    margin-top: 30px;
}

.resource-item {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border-left: 4px solid var(--accent-color);
}

.resource-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.resource-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 15px;
}

.resource-title {
    color: var(--primary-color);
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.resource-meta {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    font-size: 0.9rem;
    color: #666;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.resource-description {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.download-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.download-btn:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .resources-banner h1 {
        font-size: 2.5rem;
    }
    
    .step-title {
        font-size: 2rem;
        text-align: center;
    }
    
    .breadcrumb {
        justify-content: flex-start;
        overflow-x: auto;
        padding: 15px;
    }
    
    .courses-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .subjects-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .timeline-years {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 10px;
    }
    
    .timeline-months {
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
    }
    
    .selected-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .resource-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .resource-meta {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .resources-banner {
        padding: 80px 0 60px;
    }
    
    .resources-banner h1 {
        font-size: 2rem;
    }
    
    .course-card,
    .subject-card {
        padding: 25px 20px;
    }
    
    .timeline-months {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .month-card {
        padding: 15px 10px;
    }
}
