# SelexIQ Resources Management System

A comprehensive, dynamic resources management system for educational institutions. This system allows administrators to manage courses, subjects, and downloadable resources with a beautiful, responsive interface.

## 🌟 Features

### Public Features
- **Dynamic Course Navigation**: Users can browse courses → subjects → timeline → resources
- **Responsive Design**: Fully mobile-friendly interface
- **Beautiful UI**: Modern design with smooth animations
- **Fast Loading**: Optimized for performance
- **Download Tracking**: Tracks resource download counts

### Admin Features
- **Secure Admin Panel**: Protected login system with session management and CSRF protection
- **Dashboard**: Comprehensive overview with statistics, quick actions, and recent activity
- **Course Management**: Add, edit, delete courses with custom icons, colors, and sorting
- **Subject Management**: Organize subjects under courses with filtering and bulk operations
- **Resource Management**: Upload and manage downloadable resources with Google Drive integration
- **Analytics & Reports**: Detailed insights with charts, popular resources, and download statistics
- **Settings & Profile**: Admin profile management, password changes, and system information
- **Google Drive Integration**: Direct links to Google Drive resources with validation

## 📁 File Structure

```
selexiq/
├── config/
│   └── database.php          # Database configuration and helper functions
├── admin/
│   ├── css/
│   │   ├── admin.css         # Admin panel styles
│   │   ├── dashboard.css     # Dashboard specific styles
│   │   ├── forms.css         # Form and management styles
│   │   └── analytics.css     # Analytics page styles
│   ├── index.php             # Admin login page (main entry point)
│   ├── dashboard.php         # Admin dashboard with statistics
│   ├── courses.php           # Course management (full CRUD)
│   ├── subjects.php          # Subject management (full CRUD)
│   ├── resources.php         # Resource management (full CRUD)
│   ├── analytics.php         # Analytics and reports with charts
│   ├── settings.php          # Admin settings and profile management
│   └── logout.php            # Logout functionality
├── css/
│   ├── styles.css            # Main website styles
│   └── resources.css         # Resources page styles
├── js/
│   ├── script.js             # Main website JavaScript
│   └── resources.js          # Resources page JavaScript
├── database/
│   └── selexiq_resources.sql # Database structure and sample data
├── resources.php             # Public resources page
└── README_RESOURCES.md       # This file
```

## 🚀 Installation & Setup

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- Modern web browser

### Step 1: Database Setup
1. Create a new MySQL database named `selexiq_resources`
2. Import the SQL file:
   ```sql
   mysql -u your_username -p selexiq_resources < database/selexiq_resources.sql
   ```

### Step 2: Configuration
1. Edit `config/database.php` and update the database credentials:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'selexiq_resources');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   ```

### Step 3: File Permissions
Ensure the web server has read access to all files and write access to session storage.

### Step 4: Access the System
- **Public Resources Page**: `http://yoursite.com/resources.php`
- **Admin Panel**: `http://yoursite.com/admin/index.php`

## 🔐 Default Admin Credentials

- **Username**: `admin`
- **Password**: `admin123`

**⚠️ Important**: Change these credentials immediately after first login!

## 📊 Database Schema

### Tables Overview
- **admin_users**: Admin user accounts
- **courses**: Course categories (AP, IGCSE, A-Level, etc.)
- **subjects**: Subjects within each course
- **resources**: Downloadable resources with Google Drive links

### Sample Data Included
- 7 courses (AP Courses, MYP, IGCSE, GCSE, AS Level, A Level, IBDP)
- 25+ subjects across all courses
- Sample resources with Google Drive links
- 1 admin user account

## 🎨 Customization

### Colors
The system uses CSS custom properties for easy color customization:
```css
:root {
    --primary-color: #182B5C;    /* Dark navy blue */
    --accent-color: #FFBC00;     /* Golden yellow */
    --dark-accent: #343434;      /* Dark gray */
}
```

### Icons
- Uses Font Awesome 6 icons
- Customizable per course and subject
- Easy to change in admin panel

### Styling
- Responsive design with CSS Grid and Flexbox
- Smooth animations with AOS (Animate On Scroll)
- Modern card-based layout

## 🔧 Admin Panel Features

### Dashboard
- **System Statistics**: Overview of courses, subjects, resources, and downloads
- **Quick Actions**: Fast access to add courses, subjects, and resources
- **Recent Activity**: Latest resources added to the system
- **System Information**: Database status, PHP version, session details

### Course Management
- **Full CRUD Operations**: Add, edit, delete, and view courses
- **Custom Styling**: Set custom icons (Font Awesome) and colors for each course
- **Sort Ordering**: Drag-and-drop or manual sort order management
- **Status Management**: Active/inactive status with filtering
- **Validation**: Prevents deletion of courses with existing subjects

### Subject Management
- **Course Association**: Organize subjects under specific courses
- **Advanced Filtering**: Filter by course, status, and other criteria
- **Custom Icons**: Font Awesome icon support for visual identification
- **Bulk Operations**: Mass edit and delete operations
- **Resource Tracking**: Shows resource count per subject

### Resource Management
- **Google Drive Integration**: Direct links with validation and testing
- **Timeline Organization**: Year and month-based categorization
- **File Management**: Support for multiple file types (PDF, DOC, PPT, etc.)
- **Metadata Tracking**: File size, download counts, and descriptions
- **Advanced Filtering**: Multi-criteria filtering and search
- **Status Control**: Active/inactive resource management

### Analytics & Reports
- **Interactive Charts**: Course downloads (pie chart) and monthly trends (line chart)
- **Popular Resources**: Top 10 most downloaded resources with rankings
- **File Type Distribution**: Visual breakdown of resource types
- **Recent Activity**: Timeline of recent additions and changes
- **Download Statistics**: Total downloads, averages, and trends

### Settings & Profile
- **Profile Management**: Update username and email address
- **Password Security**: Change password with current password verification
- **Account Information**: Creation date, last update, session details
- **System Information**: PHP version, database version, server details
- **Security Actions**: Clear other sessions, backup options

## 🌐 Google Drive Integration

### Supported Link Formats
- `https://drive.google.com/file/d/FILE_ID/view`
- `https://drive.google.com/open?id=FILE_ID`
- `https://docs.google.com/document/d/FILE_ID/`

### Best Practices
1. Ensure files are publicly accessible
2. Use descriptive file names
3. Organize files in Google Drive folders
4. Test links before adding to system

## 🔒 Security Features

- **CSRF Protection**: All forms protected against CSRF attacks
- **SQL Injection Prevention**: Prepared statements used throughout
- **XSS Protection**: All output properly escaped
- **Session Security**: Secure session management with timeouts
- **Input Validation**: Server-side validation for all inputs

## 📱 Mobile Responsiveness

- Fully responsive design
- Touch-friendly interface
- Optimized for all screen sizes
- Progressive enhancement

## 🚀 Performance Optimizations

- **Lazy Loading**: Resources loaded on demand
- **Efficient Queries**: Optimized database queries
- **Caching**: Browser caching for static assets
- **Minified Assets**: Compressed CSS and JavaScript
- **Image Optimization**: Optimized images and icons

## 🛠️ Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `config/database.php`
   - Ensure MySQL server is running
   - Verify database exists

2. **Admin Login Issues**
   - Use default credentials: admin/admin123
   - Check session configuration
   - Clear browser cache

3. **Resources Not Loading**
   - Check JavaScript console for errors
   - Verify AJAX endpoints are accessible
   - Check database data integrity

4. **Google Drive Links Not Working**
   - Ensure files are publicly accessible
   - Use correct link format
   - Test links manually

## 📈 Future Enhancements

- **File Upload**: Direct file upload to server
- **User Roles**: Multiple admin roles with different permissions
- **Analytics**: Detailed download and usage analytics
- **Search**: Advanced search and filtering
- **API**: RESTful API for external integrations
- **Notifications**: Email notifications for new resources

## 🤝 Support

For support and questions:
1. Check this documentation
2. Review the code comments
3. Test with sample data
4. Contact the development team

## 📄 License

This project is part of the SelexIQ educational platform. All rights reserved.

---

**Created for SelexIQ Education Support Services**  
*Shaping Scholars for Top-Tier University Admissions*
