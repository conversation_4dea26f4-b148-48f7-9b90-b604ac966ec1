/* Admin Forms Styles */

/* Form Layout */
.admin-form {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 30px;
    border: 1px solid #e9ecef;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(255, 188, 0, 0.1);
}

.form-group small {
    display: block;
    color: #666;
    font-size: 0.85rem;
    margin-top: 5px;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-group input[type="color"] {
    height: 45px;
    padding: 5px;
    cursor: pointer;
}

.form-group input[type="number"] {
    max-width: 150px;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 0.95rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* Courses Grid */
.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
}

.course-item {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.course-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
    border-color: var(--accent-color);
}

.course-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.course-icon {
    font-size: 2.5rem;
    transition: all 0.3s ease;
}

.course-status {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.course-status.active {
    background: #d4edda;
    color: #155724;
}

.course-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

.course-item h3 {
    color: var(--primary-color);
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.course-item p {
    color: #666;
    line-height: 1.5;
    margin-bottom: 20px;
    min-height: 40px;
}

.course-stats {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.stat {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    font-size: 0.9rem;
    font-weight: 500;
}

.stat i {
    color: var(--accent-color);
}

.course-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.btn-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e9ecef;
    cursor: pointer;
}

.btn-icon:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.btn-icon.edit:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    border-color: var(--accent-color);
}

.btn-icon.delete:hover {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

/* Subjects Grid */
.subjects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.subject-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.subject-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
    border-color: var(--accent-color);
}

.subject-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.subject-icon {
    font-size: 2rem;
    color: var(--primary-color);
}

.subject-item h4 {
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.subject-course {
    color: var(--accent-color);
    font-size: 0.85rem;
    font-weight: 500;
    margin-bottom: 10px;
}

.subject-item p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 15px;
    min-height: 35px;
}

.subject-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

/* Resources List */
.resources-list {
    display: grid;
    gap: 20px;
}

.resource-item {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border-left: 4px solid var(--accent-color);
}

.resource-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

.resource-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 15px;
}

.resource-title {
    color: var(--primary-color);
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.resource-meta {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 10px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.resource-description {
    color: #666;
    line-height: 1.5;
    margin-bottom: 15px;
}

.resource-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Filter Bar */
.filter-bar {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--primary-color);
}

.filter-group select {
    padding: 8px 12px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.9rem;
    min-width: 150px;
}

.filter-actions {
    margin-left: auto;
    display: flex;
    gap: 10px;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.85rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .admin-form {
        padding: 20px;
    }
    
    .courses-grid,
    .subjects-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .course-item,
    .subject-item,
    .resource-item {
        padding: 20px;
    }
    
    .course-header,
    .subject-header,
    .resource-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .course-actions,
    .subject-actions,
    .resource-actions {
        justify-content: flex-start;
    }
    
    .filter-bar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-actions {
        margin-left: 0;
        justify-content: center;
    }
    
    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .admin-form {
        padding: 15px;
    }
    
    .course-item,
    .subject-item,
    .resource-item {
        padding: 15px;
    }
    
    .course-stats,
    .resource-meta {
        flex-direction: column;
        gap: 8px;
    }
    
    .btn-icon {
        width: 35px;
        height: 35px;
    }
}
