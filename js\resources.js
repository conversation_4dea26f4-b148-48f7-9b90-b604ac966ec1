/**
 * SelexIQ Resources Page JavaScript
 * Handles dynamic loading and navigation for the resources system
 */

// Global variables
let currentStep = 'courses';
let selectedCourse = null;
let selectedSubject = null;
let selectedYear = null;
let selectedMonth = null;

// Month names for display
const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
];

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    initializeAOS();
    setupEventListeners();
    updateBreadcrumb();
});

// Initialize AOS (Animate On Scroll)
function initializeAOS() {
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-out-cubic',
            once: true,
            offset: 100
        });
    }
}

// Setup event listeners
function setupEventListeners() {
    // Course card clicks
    document.querySelectorAll('.course-card').forEach(card => {
        card.addEventListener('click', function() {
            const courseId = this.dataset.courseId;
            const courseName = this.dataset.courseName;
            selectCourse(courseId, courseName);
        });
    });

    // Breadcrumb navigation
    document.querySelectorAll('.breadcrumb-item').forEach(item => {
        item.addEventListener('click', function() {
            const step = this.dataset.step;
            if (this.classList.contains('completed') || this.classList.contains('active')) {
                goToStep(step);
            }
        });
    });

    // Mobile menu toggle (if exists)
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    if (mobileToggle) {
        mobileToggle.addEventListener('click', function() {
            const nav = document.querySelector('nav ul');
            nav.classList.toggle('active');
        });
    }
}

// Navigate to a specific step
function goToStep(step) {
    // Hide all steps
    document.querySelectorAll('.resources-step').forEach(stepEl => {
        stepEl.classList.remove('active');
    });

    // Show target step
    document.getElementById(`step-${step}`).classList.add('active');
    currentStep = step;
    updateBreadcrumb();

    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// Update breadcrumb navigation
function updateBreadcrumb() {
    const breadcrumbItems = document.querySelectorAll('.breadcrumb-item');
    
    breadcrumbItems.forEach(item => {
        item.classList.remove('active', 'completed');
        const step = item.dataset.step;
        
        if (step === currentStep) {
            item.classList.add('active');
        } else if (isStepCompleted(step)) {
            item.classList.add('completed');
        }
    });
}

// Check if a step is completed
function isStepCompleted(step) {
    switch (step) {
        case 'courses':
            return selectedCourse !== null;
        case 'subjects':
            return selectedSubject !== null;
        case 'timeline':
            return selectedYear !== null && selectedMonth !== null;
        case 'resources':
            return false; // Final step
        default:
            return false;
    }
}

// Select a course
function selectCourse(courseId, courseName) {
    selectedCourse = { id: courseId, name: courseName };
    
    // Update breadcrumb text
    document.getElementById('breadcrumb-subject').textContent = `${courseName} Subjects`;
    document.getElementById('selected-course-name').textContent = courseName;
    
    // Load subjects
    loadSubjects(courseId);
    goToStep('subjects');
}

// Load subjects for a course
async function loadSubjects(courseId) {
    showLoading();
    
    try {
        const response = await fetch(`resources.php?action=get_subjects&course_id=${courseId}`);
        const subjects = await response.json();
        
        const container = document.getElementById('subjects-container');
        container.innerHTML = '';
        
        if (subjects.length === 0) {
            container.innerHTML = `
                <div class="no-data">
                    <i class="fas fa-info-circle"></i>
                    <h3>No Subjects Available</h3>
                    <p>There are currently no subjects available for this course.</p>
                </div>
            `;
        } else {
            subjects.forEach((subject, index) => {
                const subjectCard = createSubjectCard(subject, index);
                container.appendChild(subjectCard);
            });
        }
        
        // Reinitialize AOS for new elements
        if (typeof AOS !== 'undefined') {
            AOS.refresh();
        }
        
    } catch (error) {
        console.error('Error loading subjects:', error);
        showError('Failed to load subjects. Please try again.');
    } finally {
        hideLoading();
    }
}

// Create subject card element
function createSubjectCard(subject, index) {
    const card = document.createElement('div');
    card.className = 'subject-card';
    card.dataset.subjectId = subject.id;
    card.dataset.subjectName = subject.name;
    card.setAttribute('data-aos', 'fade-up');
    card.setAttribute('data-aos-delay', index * 100);
    
    card.innerHTML = `
        <div class="subject-icon">
            <i class="${subject.icon}"></i>
        </div>
        <h4>${escapeHtml(subject.name)}</h4>
        <p>${escapeHtml(subject.description || '')}</p>
    `;
    
    card.addEventListener('click', function() {
        selectSubject(subject.id, subject.name);
    });
    
    return card;
}

// Select a subject
function selectSubject(subjectId, subjectName) {
    selectedSubject = { id: subjectId, name: subjectName };
    
    // Update breadcrumb and header text
    document.getElementById('breadcrumb-timeline').textContent = `${subjectName} Timeline`;
    document.getElementById('selected-subject-name').textContent = subjectName;
    
    // Load timeline
    loadTimeline(subjectId);
    goToStep('timeline');
}

// Load timeline (years and months) for a subject
async function loadTimeline(subjectId) {
    showLoading();
    
    try {
        const response = await fetch(`resources.php?action=get_years_months&subject_id=${subjectId}`);
        const timeline = await response.json();
        
        const container = document.getElementById('timeline-container');
        container.innerHTML = '';
        
        if (timeline.length === 0) {
            container.innerHTML = `
                <div class="no-data">
                    <i class="fas fa-calendar-times"></i>
                    <h3>No Resources Available</h3>
                    <p>There are currently no resources available for this subject.</p>
                </div>
            `;
        } else {
            // Group by year
            const yearGroups = {};
            timeline.forEach(item => {
                if (!yearGroups[item.year]) {
                    yearGroups[item.year] = [];
                }
                yearGroups[item.year].push(item.month);
            });
            
            // Create year buttons
            const yearsContainer = document.createElement('div');
            yearsContainer.className = 'timeline-years';
            
            Object.keys(yearGroups).sort((a, b) => b - a).forEach(year => {
                const yearBtn = document.createElement('button');
                yearBtn.className = 'year-btn';
                yearBtn.textContent = year;
                yearBtn.dataset.year = year;
                
                yearBtn.addEventListener('click', function() {
                    selectYear(year, yearGroups[year]);
                });
                
                yearsContainer.appendChild(yearBtn);
            });
            
            container.appendChild(yearsContainer);
            
            // Create months container
            const monthsContainer = document.createElement('div');
            monthsContainer.className = 'timeline-months';
            monthsContainer.id = 'months-container';
            container.appendChild(monthsContainer);
            
            // Auto-select first year
            if (Object.keys(yearGroups).length > 0) {
                const firstYear = Object.keys(yearGroups).sort((a, b) => b - a)[0];
                selectYear(firstYear, yearGroups[firstYear]);
            }
        }
        
    } catch (error) {
        console.error('Error loading timeline:', error);
        showError('Failed to load timeline. Please try again.');
    } finally {
        hideLoading();
    }
}

// Select a year and show its months
function selectYear(year, months) {
    selectedYear = year;
    
    // Update year button states
    document.querySelectorAll('.year-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.year === year) {
            btn.classList.add('active');
        }
    });
    
    // Show months for selected year
    const monthsContainer = document.getElementById('months-container');
    monthsContainer.innerHTML = '';
    
    months.sort((a, b) => b - a).forEach(month => {
        const monthCard = document.createElement('div');
        monthCard.className = 'month-card';
        monthCard.dataset.month = month;
        
        monthCard.innerHTML = `
            <div class="month-name">${monthNames[month - 1]}</div>
        `;
        
        monthCard.addEventListener('click', function() {
            selectMonth(month);
        });
        
        monthsContainer.appendChild(monthCard);
    });
}

// Select a month
function selectMonth(month) {
    selectedMonth = month;
    
    // Update month card states
    document.querySelectorAll('.month-card').forEach(card => {
        card.classList.remove('active');
        if (card.dataset.month == month) {
            card.classList.add('active');
        }
    });
    
    // Update final info display
    document.getElementById('final-course-name').textContent = selectedCourse.name;
    document.getElementById('final-subject-name').textContent = selectedSubject.name;
    document.getElementById('final-timeline').textContent = `${monthNames[month - 1]} ${selectedYear}`;
    
    // Load resources
    loadResources(selectedSubject.id, selectedYear, month);
    goToStep('resources');
}

// Load resources for selected subject, year, and month
async function loadResources(subjectId, year, month) {
    showLoading();
    
    try {
        const response = await fetch(`resources.php?action=get_resources&subject_id=${subjectId}&year=${year}&month=${month}`);
        const resources = await response.json();
        
        const container = document.getElementById('resources-container');
        container.innerHTML = '';
        
        if (resources.length === 0) {
            container.innerHTML = `
                <div class="no-data">
                    <i class="fas fa-file-times"></i>
                    <h3>No Resources Found</h3>
                    <p>There are currently no resources available for the selected period.</p>
                </div>
            `;
        } else {
            resources.forEach((resource, index) => {
                const resourceItem = createResourceItem(resource, index);
                container.appendChild(resourceItem);
            });
        }
        
        // Reinitialize AOS for new elements
        if (typeof AOS !== 'undefined') {
            AOS.refresh();
        }
        
    } catch (error) {
        console.error('Error loading resources:', error);
        showError('Failed to load resources. Please try again.');
    } finally {
        hideLoading();
    }
}

// Create resource item element
function createResourceItem(resource, index) {
    const item = document.createElement('div');
    item.className = 'resource-item';
    item.setAttribute('data-aos', 'fade-up');
    item.setAttribute('data-aos-delay', index * 100);
    
    item.innerHTML = `
        <div class="resource-header">
            <div>
                <h3 class="resource-title">${escapeHtml(resource.title)}</h3>
                <div class="resource-meta">
                    <span class="meta-item">
                        <i class="fas fa-file-alt"></i>
                        ${escapeHtml(resource.file_type)}
                    </span>
                    ${resource.file_size ? `
                        <span class="meta-item">
                            <i class="fas fa-hdd"></i>
                            ${escapeHtml(resource.file_size)}
                        </span>
                    ` : ''}
                    <span class="meta-item">
                        <i class="fas fa-download"></i>
                        ${resource.download_count} downloads
                    </span>
                </div>
            </div>
        </div>
        ${resource.description ? `
            <p class="resource-description">${escapeHtml(resource.description)}</p>
        ` : ''}
        <a href="${escapeHtml(resource.google_drive_link)}" 
           target="_blank" 
           class="download-btn"
           onclick="trackDownload(${resource.id})">
            <i class="fas fa-download"></i>
            Download Resource
        </a>
    `;
    
    return item;
}

// Track download
async function trackDownload(resourceId) {
    try {
        await fetch(`resources.php?action=track_download&resource_id=${resourceId}`);
    } catch (error) {
        console.error('Error tracking download:', error);
    }
}

// Show loading spinner
function showLoading() {
    document.getElementById('loadingSpinner').style.display = 'block';
}

// Hide loading spinner
function hideLoading() {
    document.getElementById('loadingSpinner').style.display = 'none';
}

// Show error message
function showError(message) {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = 'error-toast';
    toast.innerHTML = `
        <i class="fas fa-exclamation-circle"></i>
        <span>${escapeHtml(message)}</span>
    `;

    // Add styles
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #dc3545;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 10px;
        animation: slideInRight 0.3s ease-out;
    `;

    document.body.appendChild(toast);

    // Remove after 5 seconds
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 5000);
}

// Add CSS animations for toast
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    .no-data {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }
    .no-data i {
        font-size: 4rem;
        color: var(--accent-color);
        margin-bottom: 20px;
    }
    .no-data h3 {
        color: var(--primary-color);
        margin-bottom: 15px;
        font-size: 1.5rem;
    }
    .no-data p {
        font-size: 1.1rem;
        line-height: 1.6;
    }
`;
document.head.appendChild(style);

// Escape HTML to prevent XSS
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text ? text.replace(/[&<>"']/g, m => map[m]) : '';
}
