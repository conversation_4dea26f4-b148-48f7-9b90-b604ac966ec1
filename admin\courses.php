<?php
/**
 * Courses Management for SelexIQ Resources System
 */

require_once '../config/database.php';
requireAdminLogin();

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        $error = 'Invalid security token. Please try again.';
    } else {
        switch ($action) {
            case 'add':
                $name = sanitizeInput($_POST['name'] ?? '');
                $description = sanitizeInput($_POST['description'] ?? '');
                $icon = sanitizeInput($_POST['icon'] ?? 'fas fa-book');
                $color = sanitizeInput($_POST['color'] ?? '#182B5C');
                $sort_order = (int)($_POST['sort_order'] ?? 0);
                
                if (empty($name)) {
                    $error = 'Course name is required.';
                } else {
                    try {
                        $db->execute("
                            INSERT INTO courses (name, description, icon, color, sort_order) 
                            VALUES (?, ?, ?, ?, ?)
                        ", [$name, $description, $icon, $color, $sort_order]);
                        $message = 'Course added successfully!';
                    } catch (Exception $e) {
                        $error = 'Failed to add course. Please try again.';
                    }
                }
                break;
                
            case 'edit':
                $id = (int)($_POST['id'] ?? 0);
                $name = sanitizeInput($_POST['name'] ?? '');
                $description = sanitizeInput($_POST['description'] ?? '');
                $icon = sanitizeInput($_POST['icon'] ?? 'fas fa-book');
                $color = sanitizeInput($_POST['color'] ?? '#182B5C');
                $sort_order = (int)($_POST['sort_order'] ?? 0);
                $status = sanitizeInput($_POST['status'] ?? 'active');
                
                if (empty($name) || $id <= 0) {
                    $error = 'Invalid course data.';
                } else {
                    try {
                        $db->execute("
                            UPDATE courses 
                            SET name = ?, description = ?, icon = ?, color = ?, sort_order = ?, status = ?
                            WHERE id = ?
                        ", [$name, $description, $icon, $color, $sort_order, $status, $id]);
                        $message = 'Course updated successfully!';
                    } catch (Exception $e) {
                        $error = 'Failed to update course. Please try again.';
                    }
                }
                break;
                
            case 'delete':
                $id = (int)($_POST['id'] ?? 0);
                if ($id > 0) {
                    try {
                        // Check if course has subjects
                        $subject_count = $db->fetch("SELECT COUNT(*) as count FROM subjects WHERE course_id = ?", [$id])['count'];
                        if ($subject_count > 0) {
                            $error = 'Cannot delete course with existing subjects. Please delete subjects first.';
                        } else {
                            $db->execute("DELETE FROM courses WHERE id = ?", [$id]);
                            $message = 'Course deleted successfully!';
                        }
                    } catch (Exception $e) {
                        $error = 'Failed to delete course. Please try again.';
                    }
                }
                break;
        }
    }
}

// Get all courses
$courses = $db->fetchAll("
    SELECT c.*, 
           COUNT(s.id) as subject_count,
           COUNT(r.id) as resource_count
    FROM courses c 
    LEFT JOIN subjects s ON c.id = s.course_id AND s.status = 'active'
    LEFT JOIN resources r ON s.id = r.subject_id AND r.status = 'active'
    GROUP BY c.id 
    ORDER BY c.sort_order ASC, c.name ASC
");

// Get course for editing if requested
$edit_course = null;
if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id'])) {
    $edit_id = (int)$_GET['id'];
    $edit_course = $db->fetch("SELECT * FROM courses WHERE id = ?", [$edit_id]);
}

$csrf_token = generateCSRFToken();
$page_title = "Courses Management";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - SelexIQ Admin</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/forms.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="icon" href="../images/logo.png" type="image/png">
</head>
<body class="admin-dashboard">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-container">
            <nav class="admin-nav">
                <div class="admin-logo-container">
                    <img src="../images/logo.png" alt="SelexIQ Logo" class="admin-logo">
                </div>
                
                <div class="admin-menu">
                    <a href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="courses.php" class="active">
                        <i class="fas fa-graduation-cap"></i> Courses
                    </a>
                    <a href="subjects.php">
                        <i class="fas fa-book"></i> Subjects
                    </a>
                    <a href="resources.php">
                        <i class="fas fa-file-alt"></i> Resources
                    </a>
                    <a href="analytics.php">
                        <i class="fas fa-chart-bar"></i> Analytics
                    </a>
                    <a href="settings.php">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </div>
                
                <div class="admin-user">
                    <div class="user-info">
                        <div class="user-name"><?php echo htmlspecialchars($_SESSION['admin_username']); ?></div>
                        <div class="user-role">Administrator</div>
                    </div>
                    <a href="logout.php" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>
                    <i class="fas fa-graduation-cap"></i>
                    Courses Management
                </h1>
                <p>Manage course categories for your resources system</p>
            </div>

            <!-- Messages -->
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Add/Edit Course Form -->
            <div class="dashboard-section">
                <h2>
                    <i class="fas fa-<?php echo $edit_course ? 'edit' : 'plus'; ?>"></i>
                    <?php echo $edit_course ? 'Edit Course' : 'Add New Course'; ?>
                </h2>
                
                <form method="POST" class="admin-form">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="<?php echo $edit_course ? 'edit' : 'add'; ?>">
                    <?php if ($edit_course): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_course['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">Course Name *</label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   required 
                                   value="<?php echo htmlspecialchars($edit_course['name'] ?? ''); ?>"
                                   placeholder="e.g., AP Courses">
                        </div>
                        
                        <div class="form-group">
                            <label for="icon">Icon Class</label>
                            <input type="text" 
                                   id="icon" 
                                   name="icon" 
                                   value="<?php echo htmlspecialchars($edit_course['icon'] ?? 'fas fa-book'); ?>"
                                   placeholder="e.g., fas fa-graduation-cap">
                            <small>Use Font Awesome icon classes</small>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="color">Color</label>
                            <input type="color" 
                                   id="color" 
                                   name="color" 
                                   value="<?php echo htmlspecialchars($edit_course['color'] ?? '#182B5C'); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="sort_order">Sort Order</label>
                            <input type="number" 
                                   id="sort_order" 
                                   name="sort_order" 
                                   value="<?php echo htmlspecialchars($edit_course['sort_order'] ?? '0'); ?>"
                                   min="0">
                        </div>
                        
                        <?php if ($edit_course): ?>
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select id="status" name="status">
                                <option value="active" <?php echo ($edit_course['status'] ?? 'active') === 'active' ? 'selected' : ''; ?>>Active</option>
                                <option value="inactive" <?php echo ($edit_course['status'] ?? 'active') === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                            </select>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" 
                                  name="description" 
                                  rows="3"
                                  placeholder="Brief description of the course"><?php echo htmlspecialchars($edit_course['description'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            <?php echo $edit_course ? 'Update Course' : 'Add Course'; ?>
                        </button>
                        
                        <?php if ($edit_course): ?>
                            <a href="courses.php" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                Cancel
                            </a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>

            <!-- Courses List -->
            <div class="dashboard-section">
                <h2>
                    <i class="fas fa-list"></i>
                    Existing Courses (<?php echo count($courses); ?>)
                </h2>
                
                <?php if (empty($courses)): ?>
                    <div class="no-data">
                        <i class="fas fa-graduation-cap"></i>
                        <h3>No Courses Yet</h3>
                        <p>Start by adding your first course above.</p>
                    </div>
                <?php else: ?>
                    <div class="courses-grid">
                        <?php foreach ($courses as $course): ?>
                            <div class="course-item">
                                <div class="course-header">
                                    <div class="course-icon" style="color: <?php echo $course['color']; ?>">
                                        <i class="<?php echo $course['icon']; ?>"></i>
                                    </div>
                                    <div class="course-status <?php echo $course['status']; ?>">
                                        <?php echo ucfirst($course['status']); ?>
                                    </div>
                                </div>
                                
                                <h3><?php echo htmlspecialchars($course['name']); ?></h3>
                                <p><?php echo htmlspecialchars($course['description'] ?: 'No description'); ?></p>
                                
                                <div class="course-stats">
                                    <span class="stat">
                                        <i class="fas fa-book"></i>
                                        <?php echo $course['subject_count']; ?> subjects
                                    </span>
                                    <span class="stat">
                                        <i class="fas fa-file-alt"></i>
                                        <?php echo $course['resource_count']; ?> resources
                                    </span>
                                </div>
                                
                                <div class="course-actions">
                                    <a href="courses.php?action=edit&id=<?php echo $course['id']; ?>" 
                                       class="btn-icon edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="subjects.php?course_id=<?php echo $course['id']; ?>" 
                                       class="btn-icon">
                                        <i class="fas fa-book"></i>
                                    </a>
                                    <?php if ($course['subject_count'] == 0): ?>
                                        <button onclick="deleteCourse(<?php echo $course['id']; ?>, '<?php echo htmlspecialchars($course['name']); ?>')" 
                                                class="btn-icon delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Delete Form (Hidden) -->
    <form id="deleteForm" method="POST" style="display: none;">
        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
        <input type="hidden" name="action" value="delete">
        <input type="hidden" name="id" id="deleteId">
    </form>

    <script>
        function deleteCourse(id, name) {
            if (confirm(`Are you sure you want to delete the course "${name}"?\n\nThis action cannot be undone.`)) {
                document.getElementById('deleteId').value = id;
                document.getElementById('deleteForm').submit();
            }
        }
        
        // Auto-dismiss alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 300);
            });
        }, 5000);
    </script>
</body>
</html>
