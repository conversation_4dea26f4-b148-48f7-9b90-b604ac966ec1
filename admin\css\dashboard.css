/* Dashboard Specific Styles */

/* <PERSON> Header */
.page-header {
    margin-bottom: 40px;
    text-align: center;
}

.page-header h1 {
    color: var(--primary-color);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.page-header p {
    color: #666;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 50px;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 20px;
}

.stat-icon.courses {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-icon.subjects {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-icon.resources {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-icon.downloads {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stat-content h3 {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.stat-content p {
    color: #666;
    font-weight: 500;
    margin-bottom: 15px;
}

.stat-action a {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

.stat-action a:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

.stat-action span {
    color: #999;
    font-weight: 500;
}

/* Dashboard Sections */
.dashboard-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.dashboard-section h2 {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.action-card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 25px 20px;
    text-align: center;
    text-decoration: none;
    transition: all 0.3s ease;
    display: block;
}

.action-card:hover {
    border-color: var(--accent-color);
    background: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.action-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin: 0 auto 15px;
    transition: all 0.3s ease;
}

.action-card:hover .action-icon {
    background: var(--accent-color);
    color: var(--primary-color);
}

.action-card h4 {
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.action-card p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Resources Table */
.resources-table {
    overflow-x: auto;
}

.resources-table table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.resources-table th,
.resources-table td {
    padding: 15px 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.resources-table th {
    background: #f8f9fa;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.resources-table tr:hover {
    background: #f8f9fa;
}

.resource-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.resource-title i {
    color: var(--accent-color);
}

.date-badge {
    background: var(--primary-color);
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 500;
}

.download-count {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    font-weight: 500;
}

.download-count i {
    color: var(--accent-color);
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.btn-icon {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e9ecef;
}

.btn-icon:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-icon.edit:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    border-color: var(--accent-color);
}

.table-footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.view-all-btn {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: 2px solid var(--primary-color);
    border-radius: 25px;
    transition: all 0.3s ease;
}

.view-all-btn:hover {
    background: var(--primary-color);
    color: white;
}

/* System Info */
.system-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.info-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
}

.info-card h4 {
    color: var(--primary-color);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.info-card p {
    color: #666;
    font-weight: 500;
    margin: 0;
}

.status-good {
    color: #28a745 !important;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

/* No Data State */
.no-data {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-data i {
    font-size: 4rem;
    color: var(--accent-color);
    margin-bottom: 20px;
}

.no-data h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.no-data p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 25px;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header h1 {
        font-size: 2rem;
        flex-direction: column;
        gap: 10px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .dashboard-section {
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .resources-table {
        font-size: 0.9rem;
    }
    
    .resources-table th,
    .resources-table td {
        padding: 10px 8px;
    }
    
    .system-info {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .admin-menu {
        gap: 15px;
    }
    
    .admin-menu a {
        padding: 8px 12px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .admin-container {
        padding: 0 15px;
    }
    
    .page-header {
        margin-bottom: 30px;
    }
    
    .page-header h1 {
        font-size: 1.8rem;
    }
    
    .stat-card {
        padding: 20px;
    }
    
    .stat-content h3 {
        font-size: 2rem;
    }
    
    .dashboard-section {
        padding: 15px;
    }
    
    .action-card {
        padding: 20px 15px;
    }
}
