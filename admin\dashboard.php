<?php
/**
 * Admin Dashboard for SelexIQ Resources System
 */

require_once '../config/database.php';
requireAdminLogin();

// Get dashboard statistics
$stats = [
    'courses' => $db->fetch("SELECT COUNT(*) as count FROM courses WHERE status = 'active'")['count'],
    'subjects' => $db->fetch("SELECT COUNT(*) as count FROM subjects WHERE status = 'active'")['count'],
    'resources' => $db->fetch("SELECT COUNT(*) as count FROM resources WHERE status = 'active'")['count'],
    'total_downloads' => $db->fetch("SELECT SUM(download_count) as total FROM resources")['total'] ?? 0
];

// Get recent resources
$recent_resources = $db->fetchAll("
    SELECT r.*, s.name as subject_name, c.name as course_name 
    FROM resources r 
    JOIN subjects s ON r.subject_id = s.id 
    JOIN courses c ON s.course_id = c.id 
    WHERE r.status = 'active' 
    ORDER BY r.created_at DESC 
    LIMIT 5
");

$page_title = "Dashboard";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - SelexIQ Admin</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="icon" href="../images/logo.png" type="image/png">
</head>
<body class="admin-dashboard">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-container">
            <nav class="admin-nav">
                <div class="admin-logo-container">
                    <img src="../images/logo.png" alt="SelexIQ Logo" class="admin-logo">
                </div>
                
                <div class="admin-menu">
                    <a href="dashboard.php" class="active">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="courses.php">
                        <i class="fas fa-graduation-cap"></i> Courses
                    </a>
                    <a href="subjects.php">
                        <i class="fas fa-book"></i> Subjects
                    </a>
                    <a href="resources.php">
                        <i class="fas fa-file-alt"></i> Resources
                    </a>
                </div>
                
                <div class="admin-user">
                    <div class="user-info">
                        <div class="user-name"><?php echo htmlspecialchars($_SESSION['admin_username']); ?></div>
                        <div class="user-role">Administrator</div>
                    </div>
                    <a href="logout.php" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </h1>
                <p>Welcome back, <?php echo htmlspecialchars($_SESSION['admin_username']); ?>! Here's an overview of your resources system.</p>
            </div>

            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon courses">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['courses']); ?></h3>
                        <p>Active Courses</p>
                    </div>
                    <div class="stat-action">
                        <a href="courses.php">Manage <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon subjects">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['subjects']); ?></h3>
                        <p>Active Subjects</p>
                    </div>
                    <div class="stat-action">
                        <a href="subjects.php">Manage <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon resources">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['resources']); ?></h3>
                        <p>Available Resources</p>
                    </div>
                    <div class="stat-action">
                        <a href="resources.php">Manage <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon downloads">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['total_downloads']); ?></h3>
                        <p>Total Downloads</p>
                    </div>
                    <div class="stat-action">
                        <span>Analytics</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="dashboard-section">
                <h2>
                    <i class="fas fa-bolt"></i>
                    Quick Actions
                </h2>
                <div class="quick-actions">
                    <a href="courses.php?action=add" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <h4>Add New Course</h4>
                        <p>Create a new course category</p>
                    </a>

                    <a href="subjects.php?action=add" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <h4>Add New Subject</h4>
                        <p>Add a subject to existing course</p>
                    </a>

                    <a href="resources.php?action=add" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-upload"></i>
                        </div>
                        <h4>Upload Resource</h4>
                        <p>Add new study material</p>
                    </a>

                    <a href="../resources.php" target="_blank" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                        <h4>View Public Page</h4>
                        <p>See how users view resources</p>
                    </a>
                </div>
            </div>

            <!-- Recent Resources -->
            <div class="dashboard-section">
                <h2>
                    <i class="fas fa-clock"></i>
                    Recent Resources
                </h2>
                <div class="recent-resources">
                    <?php if (empty($recent_resources)): ?>
                        <div class="no-data">
                            <i class="fas fa-info-circle"></i>
                            <h3>No Resources Yet</h3>
                            <p>Start by adding some courses and resources to get started.</p>
                            <a href="courses.php?action=add" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add First Course
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="resources-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Resource Title</th>
                                        <th>Course</th>
                                        <th>Subject</th>
                                        <th>Date</th>
                                        <th>Downloads</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_resources as $resource): ?>
                                    <tr>
                                        <td>
                                            <div class="resource-title">
                                                <i class="fas fa-file-alt"></i>
                                                <?php echo htmlspecialchars($resource['title']); ?>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($resource['course_name']); ?></td>
                                        <td><?php echo htmlspecialchars($resource['subject_name']); ?></td>
                                        <td>
                                            <span class="date-badge">
                                                <?php echo date('M Y', mktime(0, 0, 0, $resource['month'], 1, $resource['year'])); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="download-count">
                                                <i class="fas fa-download"></i>
                                                <?php echo number_format($resource['download_count']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="<?php echo htmlspecialchars($resource['google_drive_link']); ?>" 
                                                   target="_blank" 
                                                   class="btn-icon" 
                                                   title="View Resource">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                                <a href="resources.php?action=edit&id=<?php echo $resource['id']; ?>" 
                                                   class="btn-icon edit" 
                                                   title="Edit Resource">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="table-footer">
                            <a href="resources.php" class="view-all-btn">
                                View All Resources <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- System Info -->
            <div class="dashboard-section">
                <h2>
                    <i class="fas fa-info-circle"></i>
                    System Information
                </h2>
                <div class="system-info">
                    <div class="info-card">
                        <h4>Database Status</h4>
                        <p class="status-good">
                            <i class="fas fa-check-circle"></i>
                            Connected
                        </p>
                    </div>
                    <div class="info-card">
                        <h4>PHP Version</h4>
                        <p><?php echo PHP_VERSION; ?></p>
                    </div>
                    <div class="info-card">
                        <h4>Last Login</h4>
                        <p><?php echo date('M j, Y g:i A', $_SESSION['admin_login_time']); ?></p>
                    </div>
                    <div class="info-card">
                        <h4>Session Expires</h4>
                        <p id="sessionExpiry"></p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Update session expiry countdown
        function updateSessionExpiry() {
            const loginTime = <?php echo $_SESSION['admin_login_time']; ?>;
            const timeout = <?php echo ADMIN_SESSION_TIMEOUT; ?>;
            const expiryTime = (loginTime + timeout) * 1000;
            const now = new Date().getTime();
            const timeLeft = expiryTime - now;
            
            if (timeLeft > 0) {
                const hours = Math.floor(timeLeft / (1000 * 60 * 60));
                const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                
                document.getElementById('sessionExpiry').textContent = 
                    `${hours}h ${minutes}m ${seconds}s`;
            } else {
                document.getElementById('sessionExpiry').textContent = 'Expired';
                // Optionally redirect to login
                // window.location.href = 'login.php';
            }
        }
        
        // Update every second
        setInterval(updateSessionExpiry, 1000);
        updateSessionExpiry();
        
        // Auto-refresh stats every 30 seconds
        setInterval(() => {
            // You can implement AJAX refresh here if needed
        }, 30000);
    </script>
</body>
</html>
