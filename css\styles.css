/* Global Styles */
:root {
    --primary-color: #182B5C;
    --secondary-color: #f8f9fa;
    --accent-color: #FFBC00;
    --dark-accent: #343434;
    --text-color: #333;
    --light-text: #fff;
    --dark-text: #212529;
    --border-color: #dee2e6;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --gradient-primary: linear-gradient(135deg, #182B5C, #FFBC00);
    --gradient-accent: linear-gradient(135deg, #FFBC00, #FFD700);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #fff;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    color: var(--accent-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.btn {
    display: inline-block;
    padding: 12px 28px;
    border-radius: 30px;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background-color: var(--accent-color);
    color: var(--light-text);
}

.btn-primary:hover {
    background-color: #E6A800;
    color: var(--light-text);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 188, 0, 0.3);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: var(--light-text);
}

.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-header h2 {
    font-size: 32px;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.section-header p {
    font-size: 18px;
    color: var(--text-color);
}

/* Header Styles */
header {
    background-color: #fff;
    box-shadow: var(--box-shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 15px 0;
    transition: all 0.3s ease;
}

header.sticky {
    padding: 10px 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    background-color: rgba(255, 255, 255, 0.95);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo img {
    height: 60px;
    width: auto;
    transition: all 0.3s ease;
}

header.sticky .logo img {
    height: 45px;
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 25px;
    position: relative;
}

nav ul li a {
    color: var(--text-color);
    font-weight: 500;
    padding: 5px 0;
    position: relative;
    transition: all 0.3s ease;
}

nav ul li a:hover,
nav ul li a.active {
    color: var(--primary-color);
}

nav ul li a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

nav ul li a:hover::after,
nav ul li a.active::after {
    width: 100%;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 5px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--text-color);
    margin: 3px 0;
    transition: 0.3s;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* Hero Section */
.hero {
    position: relative;
    color: var(--light-text);
    padding: 120px 0;
    text-align: left;
    overflow: hidden;
    height: 90vh;
    min-height: 650px;
    display: flex;
    align-items: center;
}

.video-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.video-background video {
    min-width: 100%;
    min-height: 100%;
    width: auto;
    height: auto;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    object-fit: cover;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 102, 204, 0.6));
}

.hero .container {
    position: relative;
    z-index: 1;
}

.hero-content {
    max-width: 650px;
    padding-left: 20px;
}

.hero-content h1 {
    font-size: 48px;
    margin-bottom: 20px;
    line-height: 1.2;
    font-weight: 800;
    text-transform: uppercase;
}

.hero-content p {
    font-size: 16px;
    margin-bottom: 30px;
    line-height: 1.7;
    max-width: 600px;
    font-weight: 300;
}

.hero .btn-primary {
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 700;
    letter-spacing: 1px;
    background-color: var(--accent-color);
    border-radius: 30px;
    text-transform: uppercase;
}

/* Hero Stats */
.hero-stats {
    display: flex;
    gap: 20px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.stat-badge {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 15px 20px;
    text-align: center;
    min-width: 120px;
    transition: var(--transition);
}

.stat-badge:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
}

.stat-number {
    display: block;
    font-size: 24px;
    font-weight: 800;
    color: var(--accent-color);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: var(--light-text);
    font-weight: 500;
}

/* Services Section */
.services {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.service-card {
    background: #fff;
    border-radius: 15px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    border: 1px solid rgba(0, 102, 204, 0.1);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.service-icon {
    margin-bottom: 25px;
    color: var(--primary-color);
    font-size: 48px;
}

.service-card h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 24px;
    font-weight: 600;
}

.service-card p {
    color: var(--text-color);
    line-height: 1.6;
    font-size: 16px;
}

.additional-note {
    text-align: center;
    padding: 20px;
    background: rgba(0, 102, 204, 0.1);
    border-radius: 10px;
    margin-top: 30px;
}

.additional-note p {
    color: var(--primary-color);
    font-size: 16px;
    margin: 0;
}

/* Animation Keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes borderPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 102, 204, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 102, 204, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 102, 204, 0);
    }
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(0, 204, 153, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(0, 204, 153, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(0, 204, 153, 0);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

@keyframes text-focus-in {
    0% {
        filter: blur(12px);
        opacity: 0;
    }
    100% {
        filter: blur(0px);
        opacity: 1;
    }
}

@keyframes slide-in-left {
    0% {
        transform: translateX(-100px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slide-in-right {
    0% {
        transform: translateX(100px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

.welcome-image-section {
    display: flex;
    align-items: center;
    margin-bottom: 80px;
    gap: 40px;
    flex-wrap: wrap;
    position: relative;
    padding: 30px;
    border-radius: 15px;
    background-color: #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.5s ease;
    overflow: hidden;
}

.welcome-image-section:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.welcome-image-section.reverse {
    flex-direction: row-reverse;
}

.welcome-text {
    flex: 1;
    min-width: 300px;
    position: relative;
    z-index: 2;
}

.animate-text {
    animation: fadeInLeft 0.8s ease-out forwards;
}

.welcome-image-section.reverse .animate-text {
    animation: fadeInRight 0.8s ease-out forwards;
}

.welcome-text h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 28px;
    font-weight: 700;
    position: relative;
    display: inline-block;
}

.welcome-text h3:after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--accent-color);
    transition: width 0.3s ease;
}

.welcome-image-section:hover .welcome-text h3:after {
    width: 100%;
}

.age-badge {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 15px;
    animation: scaleIn 0.5s ease-out forwards;
}

.age-badge.secondary {
    background-color: var(--accent-color);
}

.welcome-list {
    list-style: none;
    margin-bottom: 25px;
}

.welcome-list li {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    opacity: 0;
}

.animate-item:nth-child(1) {
    animation: fadeInUp 0.5s ease-out 0.2s forwards;
}

.animate-item:nth-child(2) {
    animation: fadeInUp 0.5s ease-out 0.4s forwards;
}

.animate-item:nth-child(3) {
    animation: fadeInUp 0.5s ease-out 0.6s forwards;
}

.animate-item:nth-child(4) {
    animation: fadeInUp 0.5s ease-out 0.8s forwards;
}

.welcome-list li i {
    color: var(--accent-color);
    margin-right: 15px;
    font-size: 18px;
    background-color: rgba(24, 43, 92, 0.1);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.welcome-image-section:hover .welcome-list li i {
    background-color: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.btn-outline {
    display: inline-block;
    padding: 10px 25px;
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    border-radius: 30px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    opacity: 0;
    animation: fadeInUp 0.5s ease-out 1s forwards;
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
}

/* Animation Classes */
.floating {
    animation: float 3s ease-in-out infinite;
}

.pulse-btn {
    animation: pulse 2s infinite;
}

.rotating-slow {
    animation: rotate 15s linear infinite;
}

.icon-bounce {
    animation: bounce 2s ease infinite;
    animation-delay: 0.5s;
}

.icon-pulse {
    animation: pulse 2s infinite;
}

.text-focus-in {
    animation: text-focus-in 1s cubic-bezier(0.550, 0.085, 0.680, 0.530) both;
}

.slide-in-left {
    animation: slide-in-left 0.8s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}

.slide-in-right {
    animation: slide-in-right 0.8s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}

/* Particles Container */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

/* Animated Heading */
.animated-heading {
    background: linear-gradient(to right, #ffffff, #00ccff, #ffffff);
    background-size: 200% auto;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-text 3s linear infinite;
}

@keyframes gradient-text {
    to {
        background-position: 200% center;
    }
}

/* Glow on hover button */
.glow-on-hover {
    position: relative;
    overflow: hidden;
}

.glow-on-hover:after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(30deg);
    transition: transform 0.5s;
    opacity: 0;
}

.glow-on-hover:hover:after {
    transform: rotate(30deg) translate(10%, 10%);
    opacity: 1;
}

/* Card overlay effect */
.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 102, 204, 0.1) 0%, rgba(0, 204, 153, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
    border-radius: 8px;
}

.program-card {
    position: relative;
    overflow: hidden;
}

.program-card:hover .card-overlay {
    opacity: 1;
}

/* Input animation */
.input-animation {
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.input-animation:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 10px rgba(255, 188, 0, 0.3);
    transform: translateY(-2px);
}

/* Counter animation */
.counter {
    display: inline-block;
    font-weight: bold;
}

/* Footer link animation */
.footer-link-animation {
    position: relative;
    padding-left: 0;
    transition: padding-left 0.3s ease;
}

.footer-link-animation:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 0;
    height: 2px;
    background-color: var(--accent-color);
    transition: width 0.3s ease;
    transform: translateY(-50%);
}

.footer-link-animation:hover {
    padding-left: 15px;
}

.footer-link-animation:hover:before {
    width: 10px;
}

/* Contact item animation */
.contact-item-animation {
    transition: transform 0.3s ease;
}

.contact-item-animation:hover {
    transform: translateX(5px);
}

/* Social icon animation */
.social-icon-animation {
    transition: transform 0.3s ease, color 0.3s ease;
}

.social-icon-animation:hover {
    transform: translateY(-5px) scale(1.2);
    color: var(--accent-color);
}

/* Logo pulse animation */
.logo-pulse {
    transition: transform 0.3s ease;
}

.logo-pulse:hover {
    transform: scale(1.05);
}

/* Back to top button */
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 99;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--accent-color);
    color: white;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.back-to-top-btn:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

/* Tilt effect for images */
.tilt-image {
    transition: transform 0.3s ease;
    transform-style: preserve-3d;
    will-change: transform;
}

.welcome-image {
    flex: 1;
    min-width: 300px;
    text-align: center;
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
}

.animate-image {
    animation: scaleIn 0.8s ease-out forwards;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 102, 204, 0.2), rgba(0, 102, 204, 0));
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.welcome-image-section:hover .image-overlay {
    opacity: 1;
}

.welcome-image img {
    max-width: 100%;
    border-radius: 10px;
    height: auto;
    object-fit: cover;
    transition: all 0.5s ease;
}

.welcome-image-section:hover .welcome-image img {
    transform: scale(1.05);
}

.welcome-icons {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    margin-top: 40px;
    gap: 20px;
}

.icon-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 120px;
}

.icon-item i {
    font-size: 36px;
    color: var(--primary-color);
    margin-bottom: 10px;
    background-color: rgba(24, 43, 92, 0.1);
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.icon-item:hover i {
    background-color: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.icon-item span {
    font-weight: 600;
    font-size: 14px;
}

/* Services Section */
.services {
    padding: 80px 0;
}

.service-cards {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: var(--box-shadow);
    padding: 30px;
    text-align: center;
    flex: 1;
    min-width: 300px;
    max-width: 400px;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
}

.card-icon {
    margin-bottom: 20px;
}

.card-icon img {
    width: 60px;
    height: 60px;
}

.card h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
}

.card p {
    margin-bottom: 20px;
}

/* Features Section */
.features {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.feature-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.feature-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    padding: 30px;
    text-align: center;
    transition: var(--transition);
    border: 1px solid #eee;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    margin-bottom: 20px;
    color: var(--primary-color);
    font-size: 40px;
    background-color: rgba(24, 43, 92, 0.1);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.feature-card h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 20px;
    font-weight: 700;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

.feature-highlights {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    padding: 40px;
    margin-top: 40px;
}

.highlight-item {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 30px;
    border-bottom: 1px solid #eee;
}

.highlight-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.highlight-icon {
    background-color: var(--accent-color);
    color: var(--primary-color);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
    font-size: 20px;
}

.highlight-content {
    flex: 1;
}

.highlight-content h4 {
    color: var(--primary-color);
    margin-bottom: 8px;
    font-size: 18px;
    font-weight: 600;
}

.highlight-content p {
    color: #666;
    line-height: 1.5;
    margin: 0;
}

/* Programs Section */
.programs {
    padding: 80px 0;
}

.program-cards {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.program-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: var(--box-shadow);
    padding: 30px;
    text-align: center;
    flex: 1;
    min-width: 300px;
    max-width: 400px;
    transition: var(--transition);
}

.program-card:hover {
    transform: translateY(-5px);
}

.program-icon {
    margin-bottom: 20px;
}

.program-icon img {
    width: 60px;
    height: 60px;
}

.program-card h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
}

/* Assessment Section */
.assessment {
    padding: 80px 0;
    background-color: var(--primary-color);
    color: var(--light-text);
    text-align: center;
}

.assessment h2 {
    margin-bottom: 40px;
    font-size: 32px;
}

.stats {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    margin-bottom: 50px;
}

.stat-item {
    padding: 20px;
}

.stat-item h3 {
    font-size: 36px;
    margin-bottom: 10px;
}

.assessment-form {
    max-width: 800px;
    margin: 0 auto;
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: var(--box-shadow);
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    flex: 1;
}

input, textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: 10px;
}

textarea {
    height: 120px;
    resize: vertical;
}

/* Footer */
footer {
    background-color: var(--dark-accent);
    color: var(--light-text);
    padding: 60px 0 20px;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 40px;
}

.footer-logo, .footer-links, .footer-contact, .footer-social {
    flex: 1;
    min-width: 250px;
    margin-bottom: 30px;
}

.footer-logo img {
    height: 60px;
    width: auto;
    margin-bottom: 20px;
}

footer h3 {
    margin-bottom: 20px;
    font-size: 20px;
}

footer ul li {
    margin-bottom: 10px;
}

footer ul li a {
    color: var(--light-text);
}

footer ul li a:hover {
    color: var(--accent-color);
}

.footer-contact p {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.footer-contact i {
    margin-right: 10px;
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--light-text);
}

.social-icons a:hover {
    background-color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* About Page Styles */
.page-banner {
    background: linear-gradient(rgba(30, 90, 168, 0.8), rgba(30, 90, 168, 0.8)), url('../images/about-banner.jpg');
    background-size: cover;
    background-position: center;
    color: var(--light-text);
    padding: 80px 0;
    text-align: center;
}

.page-banner h1 {
    font-size: 42px;
}

.about {
    padding: 80px 0;
}

.about-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 40px;
}

.about-text {
    flex: 1;
    min-width: 300px;
}

.about-text h2 {
    color: var(--primary-color);
    margin-bottom: 20px;
}

.about-text p {
    margin-bottom: 20px;
}

.about-image {
    flex: 1;
    min-width: 300px;
}

.vision {
    padding: 60px 0;
    background-color: var(--primary-color);
    color: var(--light-text);
    text-align: center;
}

.vision h2 {
    margin-bottom: 20px;
}

.values {
    padding: 80px 0;
}

.values h2 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 40px;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.value-card {
    background-color: var(--secondary-color);
    padding: 30px;
    border-radius: 8px;
    text-align: center;
}

.value-card h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
}

.stand-for {
    padding: 80px 0;
    background-color: var(--secondary-color);
}

.stand-for h2 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 40px;
}

.stand-for-list {
    max-width: 800px;
    margin: 0 auto;
}

.stand-for-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.stand-for-item .icon {
    background-color: var(--accent-color);
    color: var(--light-text);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.stand-for-conclusion {
    text-align: center;
    margin-top: 40px;
    font-weight: 600;
    font-size: 18px;
}

.team {
    padding: 80px 0;
}

.team h2 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 40px;
}

.team-members {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 30px;
}

.team-member {
    background-color: var(--secondary-color);
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    max-width: 300px;
}

.member-image {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 20px;
}

.team-member h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.team-member p {
    margin-bottom: 10px;
}

/* Performance Section */
.performance {
    padding: 80px 0;
    background: #fff;
}

.performance-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.performance-stat {
    text-align: center;
    padding: 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    min-width: 200px;
}

.performance-stat:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.stat-content h3 {
    font-size: 36px;
    font-weight: 800;
    color: var(--accent-color);
    margin-bottom: 10px;
}

.stat-content p {
    color: var(--text-color);
    font-size: 16px;
}

.performance-highlight {
    text-align: center;
    padding: 20px;
    background: rgba(255, 188, 0, 0.1);
    border-radius: 10px;
}

.highlight-text {
    color: var(--accent-color);
    text-decoration: line-through;
}

/* University Admissions Section */
.admissions {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.university-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 40px;
    flex-wrap: wrap;
}

.university-logo {
    padding: 20px;
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

.university-logo:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.university-img {
    height: 60px;
    width: auto;
    filter: grayscale(100%);
    transition: var(--transition);
}

.university-logo:hover .university-img {
    filter: grayscale(0%);
}

/* Testimonials Section */
.testimonials {
    padding: 80px 0;
    background: #fff;
}

.highlight-orange {
    color: var(--accent-color);
}

.testimonial-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.testimonial-card {
    background: #fff;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    border-left: 4px solid var(--accent-color);
}

.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.student-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.student-photo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 15px;
    object-fit: cover;
}

.student-details h4 {
    color: var(--primary-color);
    margin-bottom: 5px;
    font-size: 18px;
}

.student-grade {
    color: var(--accent-color);
    font-weight: 600;
    font-size: 14px;
}

.testimonial-text {
    color: var(--text-color);
    line-height: 1.6;
    font-style: italic;
}

/* Programs Section */
.programs {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.program-tabs {
    max-width: 1000px;
    margin: 0 auto;
}

.tab-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.tab-btn {
    padding: 12px 24px;
    border: 2px solid var(--primary-color);
    background: #fff;
    color: var(--primary-color);
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
}

.tab-btn.active,
.tab-btn:hover {
    background: var(--primary-color);
    color: #fff;
}

.tab-content {
    background: #fff;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

.tab-panel h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 28px;
}

.subject-list {
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.subject-list li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    color: var(--text-color);
}

.subject-list li:last-child {
    border-bottom: none;
}

.program-highlights h4,
.subjects-offered h4,
.program-fees h4 {
    color: var(--primary-color);
    margin: 20px 0 15px 0;
    font-size: 20px;
}

.highlight-list {
    list-style: none;
    padding: 0;
}

.highlight-list li {
    padding: 10px 0;
    display: flex;
    align-items: center;
    color: var(--text-color);
}

.highlight-list li i {
    color: var(--accent-color);
    margin-right: 10px;
}

.fee-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.fee-option {
    display: flex;
    align-items: center;
    padding: 15px;
    background: rgba(24, 43, 92, 0.1);
    border-radius: 10px;
}

.fee-option i {
    color: var(--primary-color);
    margin-right: 15px;
    font-size: 20px;
}

/* Why SelexIQ Section */
.why-selexiq {
    padding: 80px 0;
    background: #fff;
}

.why-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 40px;
    margin-bottom: 60px;
}

.why-feature {
    padding: 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

.why-feature:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.why-feature .feature-icon {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.why-feature h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 22px;
}

.why-feature p {
    color: var(--text-color);
    line-height: 1.6;
    margin-bottom: 15px;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.feature-list li {
    padding: 5px 0;
    color: var(--text-color);
    position: relative;
    padding-left: 20px;
}

.feature-list li::before {
    content: '•';
    color: var(--accent-color);
    position: absolute;
    left: 0;
    font-weight: bold;
}

.faq-section {
    max-width: 800px;
    margin: 0 auto;
    padding-top: 40px;
    border-top: 2px solid #eee;
}

.faq-section h3 {
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 40px;
    font-size: 28px;
}

.faq-item,
.location-info,
.class-info,
.timing-info {
    margin-bottom: 30px;
    padding: 25px;
    background: rgba(24, 43, 92, 0.05);
    border-radius: 10px;
    border-left: 4px solid var(--primary-color);
}

.faq-item h4,
.location-info h4,
.class-info h4,
.timing-info h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 20px;
}

.faq-item p,
.location-info p,
.class-info p,
.timing-info p {
    color: var(--text-color);
    line-height: 1.6;
    margin-bottom: 10px;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .hero {
        text-align: center;
        padding: 80px 0;
        height: auto;
    }

    .hero-content {
        margin: 0 auto;
    }

    .hero-content h1 {
        font-size: 32px;
    }

    .hero-content p {
        font-size: 16px;
        margin-left: auto;
        margin-right: auto;
    }

    .section-header h2 {
        font-size: 28px;
    }

    .welcome-image-section {
        flex-direction: column;
        text-align: center;
        padding: 20px;
    }

    .welcome-image-section.reverse {
        flex-direction: column;
    }

    .welcome-text {
        order: 1;
    }

    .welcome-image {
        order: 2;
        margin-top: 30px;
        max-width: 100%;
    }

    .welcome-list li {
        justify-content: center;
    }

    .welcome-text h3:after {
        left: 50%;
        transform: translateX(-50%);
    }

    .animate-text,
    .welcome-image-section.reverse .animate-text {
        animation: fadeInUp 0.8s ease-out forwards;
    }

    .welcome-icons {
        justify-content: center;
    }

    .icon-item {
        margin: 0 10px 20px;
    }

    .feature-highlights {
        padding: 25px;
    }

    .highlight-item {
        flex-direction: column;
        text-align: center;
        padding-bottom: 25px;
    }

    .highlight-icon {
        margin: 0 auto 15px;
    }

    .highlight-content h4 {
        margin-bottom: 10px;
    }

    .stats {
        flex-direction: column;
    }

    .footer-content {
        flex-direction: column;
    }

    /* Mobile Navigation */
    .mobile-menu-toggle {
        display: flex;
    }

    nav ul {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: #fff;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 50px;
        transition: left 0.3s ease;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        z-index: 999;
    }

    nav ul.active {
        left: 0;
    }

    nav ul li {
        margin: 20px 0;
    }

    nav ul li a {
        font-size: 18px;
    }

    /* Mobile Hero Stats */
    .hero-stats {
        justify-content: center;
        gap: 15px;
    }

    .stat-badge {
        min-width: 100px;
        padding: 10px 15px;
    }

    .stat-number {
        font-size: 20px;
    }

    .stat-label {
        font-size: 11px;
    }

    /* Mobile Service Grid */
    .service-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .service-card {
        padding: 30px 20px;
    }

    /* Mobile Performance Stats */
    .performance-stats {
        flex-direction: column;
        gap: 20px;
    }

    .performance-stat {
        min-width: auto;
        padding: 20px;
    }

    /* Mobile University Logos */
    .university-logos {
        gap: 20px;
    }

    .university-img {
        height: 40px;
    }

    /* Mobile Testimonials */
    .testimonial-cards {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .testimonial-card {
        padding: 20px;
    }

    .student-photo {
        width: 50px;
        height: 50px;
    }

    /* Mobile Programs */
    .tab-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .tab-btn {
        width: 100%;
        text-align: center;
    }

    .tab-content {
        padding: 20px;
    }

    .tab-panel h3 {
        font-size: 24px;
    }

    .fee-option {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    /* Mobile Why SelexIQ */
    .why-features {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .why-feature {
        padding: 20px;
    }

    .why-feature h3 {
        font-size: 20px;
    }

    .faq-section {
        padding: 20px;
    }

    .faq-item,
    .location-info,
    .class-info,
    .timing-info {
        padding: 20px;
    }
}
