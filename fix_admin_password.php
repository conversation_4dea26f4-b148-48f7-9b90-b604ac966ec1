<?php
/**
 * Fix Admin Password Script
 * Run this script to update the admin password in your database
 */

require_once 'config/database.php';

try {
    // Generate new password hash for "admin123"
    $password = 'admin123';
    $hash = password_hash($password, PASSWORD_DEFAULT);
    
    // Update the admin user password
    $result = $db->execute("UPDATE admin_users SET password = ? WHERE username = 'admin'", [$hash]);
    
    echo "✅ Admin password updated successfully!\n";
    echo "Username: admin\n";
    echo "Password: admin123\n";
    echo "\nYou can now login to the admin panel.\n";
    
    // Verify the password works
    $admin = $db->fetch("SELECT password FROM admin_users WHERE username = 'admin'");
    if ($admin && password_verify($password, $admin['password'])) {
        echo "✅ Password verification successful!\n";
    } else {
        echo "❌ Password verification failed!\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error updating password: " . $e->getMessage() . "\n";
    echo "Please check your database connection and try again.\n";
}
?>
