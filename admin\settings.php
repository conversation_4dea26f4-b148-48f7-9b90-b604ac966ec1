<?php
/**
 * Admin Settings and Profile for SelexIQ Resources System
 */

require_once '../config/database.php';
requireAdminLogin();

$message = '';
$error = '';

// Get current admin user data
$admin_user = $db->fetch("SELECT * FROM admin_users WHERE id = ?", [$_SESSION['admin_id']]);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        $error = 'Invalid security token. Please try again.';
    } else {
        switch ($action) {
            case 'update_profile':
                $username = sanitizeInput($_POST['username'] ?? '');
                $email = sanitizeInput($_POST['email'] ?? '');
                
                if (empty($username) || empty($email)) {
                    $error = 'Username and email are required.';
                } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $error = 'Please provide a valid email address.';
                } else {
                    try {
                        // Check if username/email already exists for other users
                        $existing = $db->fetch("
                            SELECT id FROM admin_users 
                            WHERE (username = ? OR email = ?) AND id != ?
                        ", [$username, $email, $_SESSION['admin_id']]);
                        
                        if ($existing) {
                            $error = 'Username or email already exists.';
                        } else {
                            $db->execute("
                                UPDATE admin_users 
                                SET username = ?, email = ? 
                                WHERE id = ?
                            ", [$username, $email, $_SESSION['admin_id']]);
                            
                            $_SESSION['admin_username'] = $username;
                            $admin_user['username'] = $username;
                            $admin_user['email'] = $email;
                            $message = 'Profile updated successfully!';
                        }
                    } catch (Exception $e) {
                        $error = 'Failed to update profile. Please try again.';
                    }
                }
                break;
                
            case 'change_password':
                $current_password = $_POST['current_password'] ?? '';
                $new_password = $_POST['new_password'] ?? '';
                $confirm_password = $_POST['confirm_password'] ?? '';
                
                if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
                    $error = 'All password fields are required.';
                } elseif (!password_verify($current_password, $admin_user['password'])) {
                    $error = 'Current password is incorrect.';
                } elseif ($new_password !== $confirm_password) {
                    $error = 'New passwords do not match.';
                } elseif (strlen($new_password) < 6) {
                    $error = 'New password must be at least 6 characters long.';
                } else {
                    try {
                        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                        $db->execute("
                            UPDATE admin_users 
                            SET password = ? 
                            WHERE id = ?
                        ", [$hashed_password, $_SESSION['admin_id']]);
                        
                        $message = 'Password changed successfully!';
                    } catch (Exception $e) {
                        $error = 'Failed to change password. Please try again.';
                    }
                }
                break;
                
            case 'clear_sessions':
                // This would clear all sessions except current one
                // For now, just show a success message
                $message = 'All other sessions have been cleared.';
                break;
        }
    }
}

// Get system information
$system_info = [
    'php_version' => PHP_VERSION,
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'database_version' => $db->fetch("SELECT VERSION() as version")['version'] ?? 'Unknown',
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'memory_limit' => ini_get('memory_limit'),
    'session_timeout' => ADMIN_SESSION_TIMEOUT / 60 . ' minutes'
];

$csrf_token = generateCSRFToken();
$page_title = "Settings";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - SelexIQ Admin</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/forms.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="icon" href="../images/logo.png" type="image/png">
</head>
<body class="admin-dashboard">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-container">
            <nav class="admin-nav">
                <div class="admin-logo-container">
                    <img src="../images/logo.png" alt="SelexIQ Logo" class="admin-logo">
                </div>
                
                <div class="admin-menu">
                    <a href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="courses.php">
                        <i class="fas fa-graduation-cap"></i> Courses
                    </a>
                    <a href="subjects.php">
                        <i class="fas fa-book"></i> Subjects
                    </a>
                    <a href="resources.php">
                        <i class="fas fa-file-alt"></i> Resources
                    </a>
                    <a href="analytics.php">
                        <i class="fas fa-chart-bar"></i> Analytics
                    </a>
                    <a href="settings.php" class="active">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </div>
                
                <div class="admin-user">
                    <div class="user-info">
                        <div class="user-name"><?php echo htmlspecialchars($_SESSION['admin_username']); ?></div>
                        <div class="user-role">Administrator</div>
                    </div>
                    <a href="logout.php" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>
                    <i class="fas fa-cog"></i>
                    Settings
                </h1>
                <p>Manage your account settings and system preferences</p>
            </div>

            <!-- Messages -->
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Profile Settings -->
            <div class="dashboard-section">
                <h2>
                    <i class="fas fa-user"></i>
                    Profile Settings
                </h2>
                
                <form method="POST" class="admin-form">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="update_profile">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="username">Username *</label>
                            <input type="text" 
                                   id="username" 
                                   name="username" 
                                   required 
                                   value="<?php echo htmlspecialchars($admin_user['username']); ?>"
                                   placeholder="Enter username">
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" 
                                   id="email" 
                                   name="email" 
                                   required 
                                   value="<?php echo htmlspecialchars($admin_user['email']); ?>"
                                   placeholder="Enter email address">
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Update Profile
                        </button>
                    </div>
                </form>
            </div>

            <!-- Password Change -->
            <div class="dashboard-section">
                <h2>
                    <i class="fas fa-lock"></i>
                    Change Password
                </h2>
                
                <form method="POST" class="admin-form" id="passwordForm">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="change_password">
                    
                    <div class="form-group">
                        <label for="current_password">Current Password *</label>
                        <input type="password" 
                               id="current_password" 
                               name="current_password" 
                               required 
                               placeholder="Enter current password">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="new_password">New Password *</label>
                            <input type="password" 
                                   id="new_password" 
                                   name="new_password" 
                                   required 
                                   minlength="6"
                                   placeholder="Enter new password">
                            <small>Minimum 6 characters</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">Confirm New Password *</label>
                            <input type="password" 
                                   id="confirm_password" 
                                   name="confirm_password" 
                                   required 
                                   minlength="6"
                                   placeholder="Confirm new password">
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-key"></i>
                            Change Password
                        </button>
                    </div>
                </form>
            </div>

            <!-- Account Information -->
            <div class="dashboard-section">
                <h2>
                    <i class="fas fa-info-circle"></i>
                    Account Information
                </h2>
                
                <div class="info-grid">
                    <div class="info-item">
                        <label>Account Created</label>
                        <value><?php echo date('F j, Y g:i A', strtotime($admin_user['created_at'])); ?></value>
                    </div>
                    
                    <div class="info-item">
                        <label>Last Updated</label>
                        <value><?php echo date('F j, Y g:i A', strtotime($admin_user['updated_at'])); ?></value>
                    </div>
                    
                    <div class="info-item">
                        <label>Current Session</label>
                        <value><?php echo date('F j, Y g:i A', $_SESSION['admin_login_time']); ?></value>
                    </div>
                    
                    <div class="info-item">
                        <label>Session Expires</label>
                        <value id="sessionExpiry"></value>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="dashboard-section">
                <h2>
                    <i class="fas fa-server"></i>
                    System Information
                </h2>
                
                <div class="info-grid">
                    <?php foreach ($system_info as $key => $value): ?>
                        <div class="info-item">
                            <label><?php echo ucwords(str_replace('_', ' ', $key)); ?></label>
                            <value><?php echo htmlspecialchars($value); ?></value>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Security Actions -->
            <div class="dashboard-section">
                <h2>
                    <i class="fas fa-shield-alt"></i>
                    Security Actions
                </h2>
                
                <div class="security-actions">
                    <div class="security-item">
                        <div class="security-info">
                            <h4>Clear Other Sessions</h4>
                            <p>Log out from all other devices and sessions</p>
                        </div>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                            <input type="hidden" name="action" value="clear_sessions">
                            <button type="submit" class="btn btn-secondary">
                                <i class="fas fa-sign-out-alt"></i>
                                Clear Sessions
                            </button>
                        </form>
                    </div>
                    
                    <div class="security-item">
                        <div class="security-info">
                            <h4>Download System Backup</h4>
                            <p>Export database and configuration files</p>
                        </div>
                        <button class="btn btn-secondary" onclick="alert('Backup feature coming soon!')">
                            <i class="fas fa-download"></i>
                            Download Backup
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Update session expiry countdown
        function updateSessionExpiry() {
            const loginTime = <?php echo $_SESSION['admin_login_time']; ?>;
            const timeout = <?php echo ADMIN_SESSION_TIMEOUT; ?>;
            const expiryTime = (loginTime + timeout) * 1000;
            const now = new Date().getTime();
            const timeLeft = expiryTime - now;
            
            if (timeLeft > 0) {
                const hours = Math.floor(timeLeft / (1000 * 60 * 60));
                const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                
                document.getElementById('sessionExpiry').textContent = 
                    `${hours}h ${minutes}m ${seconds}s`;
            } else {
                document.getElementById('sessionExpiry').textContent = 'Expired';
            }
        }
        
        // Update every second
        setInterval(updateSessionExpiry, 1000);
        updateSessionExpiry();

        // Password confirmation validation
        document.getElementById('passwordForm').addEventListener('submit', function(e) {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (newPassword !== confirmPassword) {
                e.preventDefault();
                alert('New passwords do not match!');
                document.getElementById('confirm_password').focus();
                return false;
            }
        });

        // Auto-dismiss alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 300);
            });
        }, 5000);
    </script>
</body>
</html>
