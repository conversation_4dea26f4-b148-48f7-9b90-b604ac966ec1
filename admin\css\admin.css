/* SelexIQ Admin Panel Styles */

/* Admin Body */
.admin-body {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.admin-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

/* Login Container */
.login-container {
    width: 100%;
    max-width: 450px;
    position: relative;
    z-index: 2;
}

.login-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Login Header */
.login-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: 40px 30px;
    text-align: center;
    position: relative;
}

.logo-container {
    margin-bottom: 20px;
}

.login-logo {
    height: 60px;
    width: auto;
    filter: brightness(0) invert(1);
}

.login-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.login-header p {
    opacity: 0.9;
    font-size: 1rem;
}

/* Login Form */
.login-form {
    padding: 40px 30px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.form-group input[type="text"],
.form-group input[type="password"],
.form-group input[type="email"] {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-group input:focus {
    outline: none;
    border-color: var(--accent-color);
    background: white;
    box-shadow: 0 0 0 3px rgba(255, 188, 0, 0.1);
}

/* Password Input */
.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 5px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: var(--primary-color);
}

/* Checkbox */
.checkbox-label {
    display: flex !important;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    font-weight: 500 !important;
    color: #666 !important;
    margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: white;
    font-size: 12px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Login Button */
.login-btn {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 15px 20px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.login-btn:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(24, 43, 92, 0.3);
}

.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Alerts */
.alert {
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
    animation: slideInDown 0.3s ease-out;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-info {
    background: #cce7ff;
    color: #004085;
    border: 1px solid #b3d7ff;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Login Footer */
.login-footer {
    background: #f8f9fa;
    padding: 30px;
    border-top: 1px solid #e9ecef;
}

.login-info {
    text-align: center;
    margin-bottom: 25px;
}

.login-info h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.login-info p {
    margin: 5px 0;
    color: #666;
    font-family: 'Courier New', monospace;
    background: white;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    display: inline-block;
}

.security-note {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #dc3545;
    font-weight: 500;
    margin-top: 15px;
    padding: 10px;
    background: #fff5f5;
    border-radius: 8px;
    border: 1px solid #fecaca;
}

.back-to-site {
    text-align: center;
}

.back-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    padding: 10px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.back-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(-5px);
}

/* Dashboard Styles */
.admin-dashboard {
    background: #f8f9fa;
    min-height: 100vh;
}

.admin-header {
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.admin-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-logo {
    height: 50px;
    width: auto;
}

.admin-menu {
    display: flex;
    gap: 30px;
    align-items: center;
}

.admin-menu a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    padding: 10px 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.admin-menu a:hover,
.admin-menu a.active {
    background: var(--primary-color);
    color: white;
}

.admin-user {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    text-align: right;
}

.user-name {
    font-weight: 600;
    color: var(--primary-color);
}

.user-role {
    font-size: 0.85rem;
    color: #666;
}

.logout-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.logout-btn:hover {
    background: #c82333;
}

/* Main Content */
.admin-main {
    padding: 40px 0;
}

.admin-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-container {
        max-width: 100%;
        margin: 0 10px;
    }
    
    .login-card {
        border-radius: 15px;
    }
    
    .login-header,
    .login-form,
    .login-footer {
        padding: 25px 20px;
    }
    
    .login-header h1 {
        font-size: 1.5rem;
    }
    
    .admin-menu {
        flex-direction: column;
        gap: 10px;
    }
    
    .admin-nav {
        flex-direction: column;
        gap: 20px;
    }
}

@media (max-width: 480px) {
    .admin-body {
        padding: 10px;
    }
    
    .login-header,
    .login-form,
    .login-footer {
        padding: 20px 15px;
    }
    
    .form-group input {
        padding: 12px 15px;
    }
    
    .login-btn {
        padding: 12px 15px;
    }
}
