// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS animations
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: false,
        mirror: true
    });

    // Initialize particles.js
    if (document.getElementById('particles-js')) {
        particlesJS.load('particles-js', 'js/particles.json', function() {
            console.log('particles.js loaded');
        });
    }

    // Initialize vanilla-tilt for 3D hover effect
    VanillaTilt.init(document.querySelectorAll(".tilt-image"), {
        max: 15,
        speed: 400,
        glare: true,
        "max-glare": 0.2,
    });
    // Sticky header functionality
    const header = document.querySelector('header');
    const scrollThreshold = 100;

    const handleScroll = () => {
        if (window.scrollY > scrollThreshold) {
            header.classList.add('sticky');
        } else {
            header.classList.remove('sticky');
        }
    };

    window.addEventListener('scroll', handleScroll);

    // Set active nav link based on scroll position
    const sections = document.querySelectorAll('section[id]');

    const highlightNavOnScroll = () => {
        const scrollPosition = window.scrollY + 150;

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = section.getAttribute('id');
            const navLink = document.querySelector(`nav a[href="#${sectionId}"]`);

            if (navLink && scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                document.querySelectorAll('nav a').forEach(link => {
                    link.classList.remove('active');
                });
                navLink.classList.add('active');
            }
        });
    };

    window.addEventListener('scroll', highlightNavOnScroll);
    highlightNavOnScroll(); // Call once on page load

    // Mobile menu toggle functionality
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const nav = document.querySelector('nav ul');

    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', function() {
            nav.classList.toggle('active');
            this.classList.toggle('active');
        });
    }

    // Close mobile menu when clicking on a link
    document.querySelectorAll('nav ul li a').forEach(link => {
        link.addEventListener('click', () => {
            nav.classList.remove('active');
            if (mobileMenuToggle) {
                mobileMenuToggle.classList.remove('active');
            }
        });
    });

    // Tab functionality for programs section
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanels = document.querySelectorAll('.tab-panel');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');

            // Remove active class from all buttons and panels
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanels.forEach(panel => panel.classList.remove('active'));

            // Add active class to clicked button and corresponding panel
            button.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            if (targetId === '#') return;

            // Close mobile menu if open
            const navUl = document.querySelector('nav ul');
            const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
            if (navUl && navUl.classList.contains('active')) {
                navUl.classList.remove('active');
                if (mobileMenuToggle) {
                    mobileMenuToggle.classList.remove('active');
                }
            }

            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                // Add a small delay to ensure smooth transition
                setTimeout(() => {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                }, 100);
            }
        });
    });

    // Form submission handling
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // Simple form validation
            let isValid = true;
            const requiredFields = form.querySelectorAll('[required]');

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('error');
                } else {
                    field.classList.remove('error');
                }
            });

            if (isValid) {
                // Show success message (in a real application, you would send the form data to a server)
                const formParent = form.parentElement;
                form.style.display = 'none';

                const successMessage = document.createElement('div');
                successMessage.className = 'success-message';
                successMessage.innerHTML = '<h3>Thank you for your submission!</h3><p>We will get back to you shortly.</p>';

                formParent.appendChild(successMessage);

                // Reset form for future use
                setTimeout(() => {
                    form.reset();
                    form.style.display = 'block';
                    successMessage.remove();
                }, 5000);
            }
        });
    });

    // Add animation on scroll
    const animateOnScroll = () => {
        const elements = document.querySelectorAll('.card, .program-card, .feature-item, .stat-item, .value-card, .team-member, .animate-section');

        elements.forEach(element => {
            const elementPosition = element.getBoundingClientRect().top;
            const screenPosition = window.innerHeight / 1.3;

            if (elementPosition < screenPosition) {
                element.classList.add('animate');

                // For welcome sections, add a class to trigger animations
                if (element.classList.contains('animate-section')) {
                    element.classList.add('animate-visible');

                    // Animate list items sequentially
                    const listItems = element.querySelectorAll('.animate-item');
                    listItems.forEach((item, index) => {
                        setTimeout(() => {
                            item.classList.add('visible');
                        }, 200 * (index + 1));
                    });

                    // Animate button
                    const button = element.querySelector('.animate-button');
                    if (button) {
                        setTimeout(() => {
                            button.classList.add('visible');
                        }, 200 * (listItems.length + 1));
                    }
                }
            }
        });
    };

    // Call on scroll
    window.addEventListener('scroll', animateOnScroll);

    // Call once on page load
    animateOnScroll();

    // Counter animation
    const startCounters = () => {
        const counters = document.querySelectorAll('.counter');
        const speed = 200;

        counters.forEach(counter => {
            const animate = () => {
                const value = +counter.getAttribute('data-count');
                const data = +counter.innerText;

                const time = value / speed;
                if (data < value) {
                    counter.innerText = Math.ceil(data + time);
                    setTimeout(animate, 1);
                } else {
                    counter.innerText = value;
                }
            }

            animate();
        });
    };

    // Start counters when they come into view
    const counterSection = document.querySelector('.stats');
    if (counterSection) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    startCounters();
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        observer.observe(counterSection);
    }

    // Back to top button
    const backToTopBtn = document.querySelector('.back-to-top');
    if (backToTopBtn) {
        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        });
    }
});

// Add CSS for mobile menu and animations
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = `
        @media (max-width: 768px) {
            header .container {
                position: relative;
            }

            .mobile-menu-btn {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                width: 30px;
                height: 20px;
                cursor: pointer;
                z-index: 1001;
            }

            .mobile-menu-btn span {
                display: block;
                width: 100%;
                height: 3px;
                background-color: var(--primary-color);
                transition: all 0.3s ease;
            }

            .mobile-menu-btn.active span:nth-child(1) {
                transform: translateY(8px) rotate(45deg);
            }

            .mobile-menu-btn.active span:nth-child(2) {
                opacity: 0;
            }

            .mobile-menu-btn.active span:nth-child(3) {
                transform: translateY(-8px) rotate(-45deg);
            }

            nav {
                position: fixed;
                top: 0;
                right: -100%;
                width: 80%;
                height: 100vh;
                background-color: #fff;
                box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
                transition: right 0.3s ease;
                z-index: 1000;
                padding: 80px 20px 20px;
            }

            nav.active {
                right: 0;
            }

            nav ul {
                flex-direction: column;
            }

            nav ul li {
                margin: 15px 0;
            }
        }

        .card, .program-card, .feature-item, .stat-item, .value-card, .team-member {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }

        .card.animate, .program-card.animate, .feature-item.animate, .stat-item.animate, .value-card.animate, .team-member.animate {
            opacity: 1;
            transform: translateY(0);
        }

        /* Animation for welcome sections */
        .animate-section {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .animate-section.animate-visible {
            opacity: 1;
            transform: translateY(0);
        }

        .animate-section .animate-item {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .animate-section .animate-item.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .animate-section .animate-button {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .animate-section .animate-button.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .error {
            border-color: #dc3545 !important;
        }

        .success-message {
            text-align: center;
            padding: 30px;
            color: #28a745;
        }
    `;

    document.head.appendChild(style);
});
