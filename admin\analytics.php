<?php
/**
 * Analytics and Reports for SelexIQ Resources System
 */

require_once '../config/database.php';
requireAdminLogin();

// Get analytics data
$analytics = [
    'total_courses' => $db->fetch("SELECT COUNT(*) as count FROM courses")['count'],
    'active_courses' => $db->fetch("SELECT COUNT(*) as count FROM courses WHERE status = 'active'")['count'],
    'total_subjects' => $db->fetch("SELECT COUNT(*) as count FROM subjects")['count'],
    'active_subjects' => $db->fetch("SELECT COUNT(*) as count FROM subjects WHERE status = 'active'")['count'],
    'total_resources' => $db->fetch("SELECT COUNT(*) as count FROM resources")['count'],
    'active_resources' => $db->fetch("SELECT COUNT(*) as count FROM resources WHERE status = 'active'")['count'],
    'total_downloads' => $db->fetch("SELECT SUM(download_count) as total FROM resources")['total'] ?? 0,
    'avg_downloads' => $db->fetch("SELECT AVG(download_count) as avg FROM resources WHERE download_count > 0")['avg'] ?? 0
];

// Most popular resources
$popular_resources = $db->fetchAll("
    SELECT r.title, r.download_count, s.name as subject_name, c.name as course_name, r.year, r.month
    FROM resources r 
    JOIN subjects s ON r.subject_id = s.id 
    JOIN courses c ON s.course_id = c.id 
    WHERE r.status = 'active' AND r.download_count > 0
    ORDER BY r.download_count DESC 
    LIMIT 10
");

// Downloads by course
$downloads_by_course = $db->fetchAll("
    SELECT c.name as course_name, c.color, SUM(r.download_count) as total_downloads, COUNT(r.id) as resource_count
    FROM courses c 
    LEFT JOIN subjects s ON c.id = s.course_id 
    LEFT JOIN resources r ON s.id = r.subject_id AND r.status = 'active'
    WHERE c.status = 'active'
    GROUP BY c.id 
    ORDER BY total_downloads DESC
");

// Downloads by month (last 12 months)
$downloads_by_month = $db->fetchAll("
    SELECT r.year, r.month, SUM(r.download_count) as downloads, COUNT(r.id) as resources
    FROM resources r 
    WHERE r.status = 'active'
    GROUP BY r.year, r.month 
    ORDER BY r.year DESC, r.month DESC 
    LIMIT 12
");

// Recent activity (last 30 days)
$recent_resources = $db->fetchAll("
    SELECT r.title, r.created_at, s.name as subject_name, c.name as course_name
    FROM resources r 
    JOIN subjects s ON r.subject_id = s.id 
    JOIN courses c ON s.course_id = c.id 
    WHERE r.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    ORDER BY r.created_at DESC 
    LIMIT 10
");

// File type distribution
$file_types = $db->fetchAll("
    SELECT file_type, COUNT(*) as count, SUM(download_count) as downloads
    FROM resources 
    WHERE status = 'active'
    GROUP BY file_type 
    ORDER BY count DESC
");

$page_title = "Analytics & Reports";
$month_names = [
    1 => 'Jan', 2 => 'Feb', 3 => 'Mar', 4 => 'Apr',
    5 => 'May', 6 => 'Jun', 7 => 'Jul', 8 => 'Aug',
    9 => 'Sep', 10 => 'Oct', 11 => 'Nov', 12 => 'Dec'
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - SelexIQ Admin</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/analytics.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="icon" href="../images/logo.png" type="image/png">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="admin-dashboard">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-container">
            <nav class="admin-nav">
                <div class="admin-logo-container">
                    <img src="../images/logo.png" alt="SelexIQ Logo" class="admin-logo">
                </div>
                
                <div class="admin-menu">
                    <a href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="courses.php">
                        <i class="fas fa-graduation-cap"></i> Courses
                    </a>
                    <a href="subjects.php">
                        <i class="fas fa-book"></i> Subjects
                    </a>
                    <a href="resources.php">
                        <i class="fas fa-file-alt"></i> Resources
                    </a>
                    <a href="analytics.php" class="active">
                        <i class="fas fa-chart-bar"></i> Analytics
                    </a>
                    <a href="settings.php">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </div>
                
                <div class="admin-user">
                    <div class="user-info">
                        <div class="user-name"><?php echo htmlspecialchars($_SESSION['admin_username']); ?></div>
                        <div class="user-role">Administrator</div>
                    </div>
                    <a href="logout.php" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>
                    <i class="fas fa-chart-bar"></i>
                    Analytics & Reports
                </h1>
                <p>Comprehensive insights into your resources system performance</p>
            </div>

            <!-- Overview Statistics -->
            <div class="analytics-overview">
                <div class="overview-card">
                    <div class="overview-icon courses">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="overview-content">
                        <h3><?php echo number_format($analytics['active_courses']); ?></h3>
                        <p>Active Courses</p>
                        <small><?php echo $analytics['total_courses']; ?> total</small>
                    </div>
                </div>

                <div class="overview-card">
                    <div class="overview-icon subjects">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="overview-content">
                        <h3><?php echo number_format($analytics['active_subjects']); ?></h3>
                        <p>Active Subjects</p>
                        <small><?php echo $analytics['total_subjects']; ?> total</small>
                    </div>
                </div>

                <div class="overview-card">
                    <div class="overview-icon resources">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="overview-content">
                        <h3><?php echo number_format($analytics['active_resources']); ?></h3>
                        <p>Active Resources</p>
                        <small><?php echo $analytics['total_resources']; ?> total</small>
                    </div>
                </div>

                <div class="overview-card">
                    <div class="overview-icon downloads">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="overview-content">
                        <h3><?php echo number_format($analytics['total_downloads']); ?></h3>
                        <p>Total Downloads</p>
                        <small><?php echo number_format($analytics['avg_downloads'], 1); ?> avg per resource</small>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="charts-section">
                <div class="chart-container">
                    <div class="dashboard-section">
                        <h2>
                            <i class="fas fa-chart-pie"></i>
                            Downloads by Course
                        </h2>
                        <canvas id="courseChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="dashboard-section">
                        <h2>
                            <i class="fas fa-chart-line"></i>
                            Downloads by Month
                        </h2>
                        <canvas id="monthChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Popular Resources -->
            <div class="dashboard-section">
                <h2>
                    <i class="fas fa-star"></i>
                    Most Popular Resources
                </h2>
                
                <?php if (empty($popular_resources)): ?>
                    <div class="no-data">
                        <i class="fas fa-chart-bar"></i>
                        <h3>No Download Data Yet</h3>
                        <p>Resource download statistics will appear here once users start downloading.</p>
                    </div>
                <?php else: ?>
                    <div class="popular-resources">
                        <?php foreach ($popular_resources as $index => $resource): ?>
                            <div class="popular-item">
                                <div class="rank">#<?php echo $index + 1; ?></div>
                                <div class="resource-info">
                                    <h4><?php echo htmlspecialchars($resource['title']); ?></h4>
                                    <p>
                                        <span class="course"><?php echo htmlspecialchars($resource['course_name']); ?></span>
                                        <span class="separator">•</span>
                                        <span class="subject"><?php echo htmlspecialchars($resource['subject_name']); ?></span>
                                        <span class="separator">•</span>
                                        <span class="date"><?php echo $month_names[$resource['month']] . ' ' . $resource['year']; ?></span>
                                    </p>
                                </div>
                                <div class="download-count">
                                    <i class="fas fa-download"></i>
                                    <?php echo number_format($resource['download_count']); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- File Types Distribution -->
            <div class="dashboard-section">
                <h2>
                    <i class="fas fa-file"></i>
                    File Types Distribution
                </h2>
                
                <?php if (empty($file_types)): ?>
                    <div class="no-data">
                        <i class="fas fa-file"></i>
                        <h3>No File Data</h3>
                        <p>File type statistics will appear here once resources are added.</p>
                    </div>
                <?php else: ?>
                    <div class="file-types-grid">
                        <?php foreach ($file_types as $type): ?>
                            <div class="file-type-card">
                                <div class="file-icon">
                                    <i class="fas fa-file-<?php echo strtolower($type['file_type']) === 'pdf' ? 'pdf' : 'alt'; ?>"></i>
                                </div>
                                <h4><?php echo htmlspecialchars($type['file_type']); ?></h4>
                                <p><?php echo number_format($type['count']); ?> files</p>
                                <small><?php echo number_format($type['downloads']); ?> downloads</small>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Recent Activity -->
            <div class="dashboard-section">
                <h2>
                    <i class="fas fa-clock"></i>
                    Recent Activity (Last 30 Days)
                </h2>
                
                <?php if (empty($recent_resources)): ?>
                    <div class="no-data">
                        <i class="fas fa-clock"></i>
                        <h3>No Recent Activity</h3>
                        <p>Recent resource additions will appear here.</p>
                    </div>
                <?php else: ?>
                    <div class="recent-activity">
                        <?php foreach ($recent_resources as $resource): ?>
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div class="activity-content">
                                    <h4><?php echo htmlspecialchars($resource['title']); ?></h4>
                                    <p>
                                        Added to <strong><?php echo htmlspecialchars($resource['course_name']); ?></strong> 
                                        → <strong><?php echo htmlspecialchars($resource['subject_name']); ?></strong>
                                    </p>
                                </div>
                                <div class="activity-time">
                                    <?php echo date('M j, Y', strtotime($resource['created_at'])); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <script>
        // Course Downloads Chart
        const courseData = <?php echo json_encode($downloads_by_course); ?>;
        const courseLabels = courseData.map(item => item.course_name);
        const courseDownloads = courseData.map(item => parseInt(item.total_downloads) || 0);
        const courseColors = courseData.map(item => item.color || '#182B5C');

        const courseCtx = document.getElementById('courseChart').getContext('2d');
        new Chart(courseCtx, {
            type: 'doughnut',
            data: {
                labels: courseLabels,
                datasets: [{
                    data: courseDownloads,
                    backgroundColor: courseColors,
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Monthly Downloads Chart
        const monthData = <?php echo json_encode($downloads_by_month); ?>;
        const monthLabels = monthData.map(item => 
            '<?php echo json_encode($month_names); ?>'[item.month] + ' ' + item.year
        );
        const monthDownloads = monthData.map(item => parseInt(item.downloads) || 0);

        const monthCtx = document.getElementById('monthChart').getContext('2d');
        new Chart(monthCtx, {
            type: 'line',
            data: {
                labels: monthLabels.reverse(),
                datasets: [{
                    label: 'Downloads',
                    data: monthDownloads.reverse(),
                    borderColor: '#FFBC00',
                    backgroundColor: 'rgba(255, 188, 0, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
</body>
</html>
