-- SelexIQ Resources Database Structure
-- Created for dynamic resources management system

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- Database: selexiq_resources
CREATE DATABASE IF NOT EXISTS `selexiq_resources` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `selexiq_resources`;

-- Table structure for table `admin_users`
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `courses`
CREATE TABLE `courses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `icon` varchar(50) DEFAULT 'fas fa-book',
  `color` varchar(7) DEFAULT '#182B5C',
  `status` enum('active','inactive') DEFAULT 'active',
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `subjects`
CREATE TABLE `subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `icon` varchar(50) DEFAULT 'fas fa-book-open',
  `status` enum('active','inactive') DEFAULT 'active',
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `course_id` (`course_id`),
  CONSTRAINT `subjects_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `resources`
CREATE TABLE `resources` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `year` int(4) NOT NULL,
  `month` int(2) NOT NULL,
  `title` varchar(200) NOT NULL,
  `description` text,
  `google_drive_link` text NOT NULL,
  `file_type` varchar(20) DEFAULT 'PDF',
  `file_size` varchar(20),
  `download_count` int(11) DEFAULT 0,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `subject_id` (`subject_id`),
  KEY `year_month` (`year`,`month`),
  CONSTRAINT `resources_ibfk_1` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default admin user (username: admin, password: admin123)
INSERT INTO `admin_users` (`username`, `email`, `password`) VALUES
('admin', '<EMAIL>', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm');

-- Insert sample courses
INSERT INTO `courses` (`name`, `description`, `icon`, `color`, `sort_order`) VALUES
('AP Courses', 'Advanced Placement courses for college-level academics', 'fas fa-graduation-cap', '#182B5C', 1),
('MYP', 'Middle Years Programme for critical thinking development', 'fas fa-users', '#182B5C', 2),
('IGCSE', 'Cambridge International General Certificate of Secondary Education', 'fas fa-certificate', '#182B5C', 3),
('GCSE', 'General Certificate of Secondary Education', 'fas fa-award', '#182B5C', 4),
('AS Level', 'Advanced Subsidiary Level qualifications', 'fas fa-medal', '#182B5C', 5),
('A Level', 'Advanced Level qualifications for university entrance', 'fas fa-trophy', '#182B5C', 6),
('IBDP', 'International Baccalaureate Diploma Programme', 'fas fa-globe', '#182B5C', 7);

-- Insert sample subjects
INSERT INTO `subjects` (`course_id`, `name`, `description`, `icon`, `sort_order`) VALUES
-- AP Courses subjects
(1, 'AP Mathematics', 'Advanced Placement Mathematics including Calculus AB & BC', 'fas fa-calculator', 1),
(1, 'AP Physics', 'Advanced Placement Physics courses', 'fas fa-atom', 2),
(1, 'AP Chemistry', 'Advanced Placement Chemistry', 'fas fa-flask', 3),
(1, 'AP Statistics', 'Advanced Placement Statistics', 'fas fa-chart-bar', 4),

-- MYP subjects
(2, 'MYP Mathematics', 'Middle Years Programme Mathematics', 'fas fa-calculator', 1),
(2, 'MYP Sciences', 'Middle Years Programme Sciences', 'fas fa-microscope', 2),
(2, 'MYP English', 'Middle Years Programme English Language & Literature', 'fas fa-book', 3),

-- IGCSE subjects
(3, 'IGCSE Mathematics', 'Cambridge IGCSE Mathematics', 'fas fa-calculator', 1),
(3, 'IGCSE Physics', 'Cambridge IGCSE Physics', 'fas fa-atom', 2),
(3, 'IGCSE Chemistry', 'Cambridge IGCSE Chemistry', 'fas fa-flask', 3),
(3, 'IGCSE Biology', 'Cambridge IGCSE Biology', 'fas fa-dna', 4),
(3, 'IGCSE English', 'Cambridge IGCSE English Language', 'fas fa-book', 5),

-- GCSE subjects
(4, 'GCSE Mathematics', 'General Certificate Mathematics', 'fas fa-calculator', 1),
(4, 'GCSE Physics', 'General Certificate Physics', 'fas fa-atom', 2),
(4, 'GCSE Chemistry', 'General Certificate Chemistry', 'fas fa-flask', 3),

-- AS Level subjects
(5, 'AS Mathematics', 'Advanced Subsidiary Mathematics', 'fas fa-calculator', 1),
(5, 'AS Physics', 'Advanced Subsidiary Physics', 'fas fa-atom', 2),

-- A Level subjects
(6, 'A Level Mathematics', 'Advanced Level Mathematics', 'fas fa-calculator', 1),
(6, 'A Level Physics', 'Advanced Level Physics', 'fas fa-atom', 2),
(6, 'A Level Chemistry', 'Advanced Level Chemistry', 'fas fa-flask', 3),
(6, 'A Level Biology', 'Advanced Level Biology', 'fas fa-dna', 4),

-- IBDP subjects
(7, 'IB Mathematics HL', 'International Baccalaureate Mathematics Higher Level', 'fas fa-calculator', 1),
(7, 'IB Physics HL', 'International Baccalaureate Physics Higher Level', 'fas fa-atom', 2),
(7, 'IB Chemistry HL', 'International Baccalaureate Chemistry Higher Level', 'fas fa-flask', 3),
(7, 'IB English HL', 'International Baccalaureate English Higher Level', 'fas fa-book', 4);

-- Insert sample resources
INSERT INTO `resources` (`subject_id`, `year`, `month`, `title`, `description`, `google_drive_link`, `file_type`) VALUES
-- AP Mathematics resources
(1, 2024, 1, 'AP Calculus AB - January Practice Tests', 'Comprehensive practice tests for AP Calculus AB', 'https://drive.google.com/file/d/1example1/view', 'PDF'),
(1, 2024, 2, 'AP Calculus BC - February Study Guide', 'Complete study guide for AP Calculus BC', 'https://drive.google.com/file/d/1example2/view', 'PDF'),
(1, 2023, 12, 'AP Mathematics - December Review', 'Year-end review materials', 'https://drive.google.com/file/d/1example3/view', 'PDF'),

-- IGCSE Mathematics resources
(8, 2024, 1, 'IGCSE Mathematics - January Past Papers', 'Collection of past papers with solutions', 'https://drive.google.com/file/d/1example4/view', 'PDF'),
(8, 2024, 2, 'IGCSE Mathematics - February Revision Notes', 'Comprehensive revision notes', 'https://drive.google.com/file/d/1example5/view', 'PDF'),

-- A Level Mathematics resources
(18, 2024, 1, 'A Level Mathematics - January Mock Exams', 'Mock examination papers', 'https://drive.google.com/file/d/1example6/view', 'PDF'),
(18, 2024, 2, 'A Level Mathematics - February Formula Sheets', 'Essential formula reference sheets', 'https://drive.google.com/file/d/1example7/view', 'PDF');

COMMIT;
