<?php
/**
 * Resources Management for SelexIQ Resources System
 */

require_once '../config/database.php';
requireAdminLogin();

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';

    if (!verifyCSRFToken($csrf_token)) {
        $error = 'Invalid security token. Please try again.';
    } else {
        switch ($action) {
            case 'add':
                $subject_id = (int)($_POST['subject_id'] ?? 0);
                $year = (int)($_POST['year'] ?? date('Y'));
                $month = (int)($_POST['month'] ?? date('n'));
                $title = sanitizeInput($_POST['title'] ?? '');
                $description = sanitizeInput($_POST['description'] ?? '');
                $google_drive_link = sanitizeInput($_POST['google_drive_link'] ?? '');
                $file_type = sanitizeInput($_POST['file_type'] ?? 'PDF');
                $file_size = sanitizeInput($_POST['file_size'] ?? '');

                if (empty($title) || $subject_id <= 0 || empty($google_drive_link)) {
                    $error = 'Title, subject, and Google Drive link are required.';
                } elseif (!validateGoogleDriveLink($google_drive_link)) {
                    $error = 'Please provide a valid Google Drive link.';
                } elseif ($year < 2020 || $year > 2030 || $month < 1 || $month > 12) {
                    $error = 'Please provide a valid year and month.';
                } else {
                    try {
                        $db->execute("
                            INSERT INTO resources (subject_id, year, month, title, description, google_drive_link, file_type, file_size)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ", [$subject_id, $year, $month, $title, $description, $google_drive_link, $file_type, $file_size]);
                        $message = 'Resource added successfully!';
                    } catch (Exception $e) {
                        $error = 'Failed to add resource. Please try again.';
                    }
                }
                break;

            case 'edit':
                $id = (int)($_POST['id'] ?? 0);
                $subject_id = (int)($_POST['subject_id'] ?? 0);
                $year = (int)($_POST['year'] ?? date('Y'));
                $month = (int)($_POST['month'] ?? date('n'));
                $title = sanitizeInput($_POST['title'] ?? '');
                $description = sanitizeInput($_POST['description'] ?? '');
                $google_drive_link = sanitizeInput($_POST['google_drive_link'] ?? '');
                $file_type = sanitizeInput($_POST['file_type'] ?? 'PDF');
                $file_size = sanitizeInput($_POST['file_size'] ?? '');
                $status = sanitizeInput($_POST['status'] ?? 'active');

                if (empty($title) || $id <= 0 || $subject_id <= 0 || empty($google_drive_link)) {
                    $error = 'Invalid resource data.';
                } elseif (!validateGoogleDriveLink($google_drive_link)) {
                    $error = 'Please provide a valid Google Drive link.';
                } elseif ($year < 2020 || $year > 2030 || $month < 1 || $month > 12) {
                    $error = 'Please provide a valid year and month.';
                } else {
                    try {
                        $db->execute("
                            UPDATE resources
                            SET subject_id = ?, year = ?, month = ?, title = ?, description = ?,
                                google_drive_link = ?, file_type = ?, file_size = ?, status = ?
                            WHERE id = ?
                        ", [$subject_id, $year, $month, $title, $description, $google_drive_link, $file_type, $file_size, $status, $id]);
                        $message = 'Resource updated successfully!';
                    } catch (Exception $e) {
                        $error = 'Failed to update resource. Please try again.';
                    }
                }
                break;

            case 'delete':
                $id = (int)($_POST['id'] ?? 0);
                if ($id > 0) {
                    try {
                        $db->execute("DELETE FROM resources WHERE id = ?", [$id]);
                        $message = 'Resource deleted successfully!';
                    } catch (Exception $e) {
                        $error = 'Failed to delete resource. Please try again.';
                    }
                }
                break;
        }
    }
}

// Get filter parameters
$filter_course = (int)($_GET['course_id'] ?? 0);
$filter_subject = (int)($_GET['subject_id'] ?? 0);
$filter_year = (int)($_GET['year'] ?? 0);
$filter_status = sanitizeInput($_GET['status'] ?? '');

// Build WHERE clause for filtering
$where_conditions = [];
$where_params = [];

if ($filter_course > 0) {
    $where_conditions[] = "c.id = ?";
    $where_params[] = $filter_course;
}

if ($filter_subject > 0) {
    $where_conditions[] = "s.id = ?";
    $where_params[] = $filter_subject;
}

if ($filter_year > 0) {
    $where_conditions[] = "r.year = ?";
    $where_params[] = $filter_year;
}

if ($filter_status) {
    $where_conditions[] = "r.status = ?";
    $where_params[] = $filter_status;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// Get all resources with course and subject information
$resources = $db->fetchAll("
    SELECT r.*, s.name as subject_name, c.name as course_name, c.color as course_color
    FROM resources r
    JOIN subjects s ON r.subject_id = s.id
    JOIN courses c ON s.course_id = c.id
    $where_clause
    ORDER BY r.year DESC, r.month DESC, c.sort_order ASC, s.sort_order ASC, r.title ASC
", $where_params);

// Get all active courses for dropdown
$courses = $db->fetchAll("
    SELECT id, name
    FROM courses
    WHERE status = 'active'
    ORDER BY sort_order ASC, name ASC
");

// Get subjects for selected course (for AJAX and initial load)
$subjects = [];
if ($filter_course > 0) {
    $subjects = $db->fetchAll("
        SELECT id, name
        FROM subjects
        WHERE course_id = ? AND status = 'active'
        ORDER BY sort_order ASC, name ASC
    ", [$filter_course]);
}

// Get all subjects if no course filter
if (empty($subjects)) {
    $subjects = $db->fetchAll("
        SELECT s.id, s.name, c.name as course_name
        FROM subjects s
        JOIN courses c ON s.course_id = c.id
        WHERE s.status = 'active' AND c.status = 'active'
        ORDER BY c.sort_order ASC, s.sort_order ASC, s.name ASC
    ");
}

// Get available years
$years = $db->fetchAll("
    SELECT DISTINCT year
    FROM resources
    ORDER BY year DESC
");

// Get resource for editing if requested
$edit_resource = null;
if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id'])) {
    $edit_id = (int)$_GET['id'];
    $edit_resource = $db->fetch("
        SELECT r.*, s.course_id
        FROM resources r
        JOIN subjects s ON r.subject_id = s.id
        WHERE r.id = ?
    ", [$edit_id]);
}

$csrf_token = generateCSRFToken();
$page_title = "Resources Management";

// Month names for display
$month_names = [
    1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
    5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
    9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - SelexIQ Admin</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/forms.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="icon" href="../images/logo.png" type="image/png">
</head>
<body class="admin-dashboard">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-container">
            <nav class="admin-nav">
                <div class="admin-logo-container">
                    <img src="../images/logo.png" alt="SelexIQ Logo" class="admin-logo">
                </div>

                <div class="admin-menu">
                    <a href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="courses.php">
                        <i class="fas fa-graduation-cap"></i> Courses
                    </a>
                    <a href="subjects.php">
                        <i class="fas fa-book"></i> Subjects
                    </a>
                    <a href="resources.php" class="active">
                        <i class="fas fa-file-alt"></i> Resources
                    </a>
                    <a href="analytics.php">
                        <i class="fas fa-chart-bar"></i> Analytics
                    </a>
                    <a href="settings.php">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </div>

                <div class="admin-user">
                    <div class="user-info">
                        <div class="user-name"><?php echo htmlspecialchars($_SESSION['admin_username']); ?></div>
                        <div class="user-role">Administrator</div>
                    </div>
                    <a href="logout.php" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>
                    <i class="fas fa-file-alt"></i>
                    Resources Management
                </h1>
                <p>Manage downloadable resources and study materials</p>
            </div>

            <!-- Messages -->
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Filter Bar -->
            <div class="filter-bar">
                <form method="GET" class="filter-form">
                    <div class="filter-group">
                        <label for="filter_course">Filter by Course</label>
                        <select id="filter_course" name="course_id" onchange="updateSubjects(); this.form.submit();">
                            <option value="">All Courses</option>
                            <?php foreach ($courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>"
                                        <?php echo $filter_course == $course['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($course['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="filter_subject">Filter by Subject</label>
                        <select id="filter_subject" name="subject_id" onchange="this.form.submit()">
                            <option value="">All Subjects</option>
                            <?php foreach ($subjects as $subject): ?>
                                <option value="<?php echo $subject['id']; ?>"
                                        <?php echo $filter_subject == $subject['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($subject['name']); ?>
                                    <?php if (isset($subject['course_name'])): ?>
                                        (<?php echo htmlspecialchars($subject['course_name']); ?>)
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="filter_year">Filter by Year</label>
                        <select id="filter_year" name="year" onchange="this.form.submit()">
                            <option value="">All Years</option>
                            <?php foreach ($years as $year_data): ?>
                                <option value="<?php echo $year_data['year']; ?>"
                                        <?php echo $filter_year == $year_data['year'] ? 'selected' : ''; ?>>
                                    <?php echo $year_data['year']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="filter_status">Filter by Status</label>
                        <select id="filter_status" name="status" onchange="this.form.submit()">
                            <option value="">All Status</option>
                            <option value="active" <?php echo $filter_status === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo $filter_status === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                    </div>

                    <div class="filter-actions">
                        <a href="resources.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-times"></i> Clear Filters
                        </a>
                    </div>
                </form>
            </div>

            <!-- Add/Edit Resource Form -->
            <div class="dashboard-section">
                <h2>
                    <i class="fas fa-<?php echo $edit_resource ? 'edit' : 'plus'; ?>"></i>
                    <?php echo $edit_resource ? 'Edit Resource' : 'Add New Resource'; ?>
                </h2>

                <?php if (empty($subjects)): ?>
                    <div class="no-data">
                        <i class="fas fa-book"></i>
                        <h3>No Subjects Available</h3>
                        <p>You need to create courses and subjects first before adding resources.</p>
                        <a href="courses.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Course First
                        </a>
                    </div>
                <?php else: ?>
                    <form method="POST" class="admin-form" id="resourceForm">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" name="action" value="<?php echo $edit_resource ? 'edit' : 'add'; ?>">
                        <?php if ($edit_resource): ?>
                            <input type="hidden" name="id" value="<?php echo $edit_resource['id']; ?>">
                        <?php endif; ?>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="course_select">Course *</label>
                                <select id="course_select" onchange="loadSubjectsForForm()" required>
                                    <option value="">Select a course</option>
                                    <?php foreach ($courses as $course): ?>
                                        <option value="<?php echo $course['id']; ?>"
                                                <?php echo ($edit_resource['course_id'] ?? $filter_course) == $course['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($course['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="subject_id">Subject *</label>
                                <select id="subject_id" name="subject_id" required>
                                    <option value="">Select a subject</option>
                                    <?php if ($edit_resource): ?>
                                        <?php foreach ($subjects as $subject): ?>
                                            <option value="<?php echo $subject['id']; ?>"
                                                    <?php echo $edit_resource['subject_id'] == $subject['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($subject['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="title">Resource Title *</label>
                            <input type="text"
                                   id="title"
                                   name="title"
                                   required
                                   value="<?php echo htmlspecialchars($edit_resource['title'] ?? ''); ?>"
                                   placeholder="e.g., Mathematics Past Papers - January 2024">
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="year">Year *</label>
                                <select id="year" name="year" required>
                                    <?php for ($y = date('Y') + 1; $y >= 2020; $y--): ?>
                                        <option value="<?php echo $y; ?>"
                                                <?php echo ($edit_resource['year'] ?? date('Y')) == $y ? 'selected' : ''; ?>>
                                            <?php echo $y; ?>
                                        </option>
                                    <?php endfor; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="month">Month *</label>
                                <select id="month" name="month" required>
                                    <?php foreach ($month_names as $num => $name): ?>
                                        <option value="<?php echo $num; ?>"
                                                <?php echo ($edit_resource['month'] ?? date('n')) == $num ? 'selected' : ''; ?>>
                                            <?php echo $name; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="file_type">File Type</label>
                                <select id="file_type" name="file_type">
                                    <option value="PDF" <?php echo ($edit_resource['file_type'] ?? 'PDF') === 'PDF' ? 'selected' : ''; ?>>PDF</option>
                                    <option value="DOC" <?php echo ($edit_resource['file_type'] ?? 'PDF') === 'DOC' ? 'selected' : ''; ?>>DOC</option>
                                    <option value="DOCX" <?php echo ($edit_resource['file_type'] ?? 'PDF') === 'DOCX' ? 'selected' : ''; ?>>DOCX</option>
                                    <option value="PPT" <?php echo ($edit_resource['file_type'] ?? 'PDF') === 'PPT' ? 'selected' : ''; ?>>PPT</option>
                                    <option value="PPTX" <?php echo ($edit_resource['file_type'] ?? 'PDF') === 'PPTX' ? 'selected' : ''; ?>>PPTX</option>
                                    <option value="XLS" <?php echo ($edit_resource['file_type'] ?? 'PDF') === 'XLS' ? 'selected' : ''; ?>>XLS</option>
                                    <option value="XLSX" <?php echo ($edit_resource['file_type'] ?? 'PDF') === 'XLSX' ? 'selected' : ''; ?>>XLSX</option>
                                    <option value="ZIP" <?php echo ($edit_resource['file_type'] ?? 'PDF') === 'ZIP' ? 'selected' : ''; ?>>ZIP</option>
                                    <option value="Other" <?php echo ($edit_resource['file_type'] ?? 'PDF') === 'Other' ? 'selected' : ''; ?>>Other</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="file_size">File Size</label>
                                <input type="text"
                                       id="file_size"
                                       name="file_size"
                                       value="<?php echo htmlspecialchars($edit_resource['file_size'] ?? ''); ?>"
                                       placeholder="e.g., 2.5 MB">
                                <small>Optional - helps users know download size</small>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="google_drive_link">Google Drive Link *</label>
                            <input type="url"
                                   id="google_drive_link"
                                   name="google_drive_link"
                                   required
                                   value="<?php echo htmlspecialchars($edit_resource['google_drive_link'] ?? ''); ?>"
                                   placeholder="https://drive.google.com/file/d/FILE_ID/view">
                            <small>Make sure the file is publicly accessible</small>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea id="description"
                                      name="description"
                                      rows="3"
                                      placeholder="Brief description of the resource content"><?php echo htmlspecialchars($edit_resource['description'] ?? ''); ?></textarea>
                        </div>

                        <?php if ($edit_resource): ?>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="status">Status</label>
                                <select id="status" name="status">
                                    <option value="active" <?php echo ($edit_resource['status'] ?? 'active') === 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?php echo ($edit_resource['status'] ?? 'active') === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                </select>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                <?php echo $edit_resource ? 'Update Resource' : 'Add Resource'; ?>
                            </button>

                            <?php if ($edit_resource): ?>
                                <a href="resources.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                    Cancel
                                </a>
                            <?php endif; ?>

                            <button type="button" class="btn btn-secondary" onclick="testGoogleDriveLink()">
                                <i class="fas fa-external-link-alt"></i>
                                Test Link
                            </button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>

            <!-- Resources List -->
            <div class="dashboard-section">
                <h2>
                    <i class="fas fa-list"></i>
                    Existing Resources (<?php echo count($resources); ?>)
                </h2>

                <?php if (empty($resources)): ?>
                    <div class="no-data">
                        <i class="fas fa-file-alt"></i>
                        <h3>No Resources Yet</h3>
                        <p>Start by adding your first resource above.</p>
                    </div>
                <?php else: ?>
                    <div class="resources-list">
                        <?php foreach ($resources as $resource): ?>
                            <div class="resource-item">
                                <div class="resource-header">
                                    <div>
                                        <h3 class="resource-title"><?php echo htmlspecialchars($resource['title']); ?></h3>
                                        <div class="resource-meta">
                                            <span class="meta-item" style="color: <?php echo $resource['course_color']; ?>">
                                                <i class="fas fa-graduation-cap"></i>
                                                <?php echo htmlspecialchars($resource['course_name']); ?>
                                            </span>
                                            <span class="meta-item">
                                                <i class="fas fa-book"></i>
                                                <?php echo htmlspecialchars($resource['subject_name']); ?>
                                            </span>
                                            <span class="meta-item">
                                                <i class="fas fa-calendar"></i>
                                                <?php echo $month_names[$resource['month']] . ' ' . $resource['year']; ?>
                                            </span>
                                            <span class="meta-item">
                                                <i class="fas fa-file-alt"></i>
                                                <?php echo htmlspecialchars($resource['file_type']); ?>
                                            </span>
                                            <?php if ($resource['file_size']): ?>
                                                <span class="meta-item">
                                                    <i class="fas fa-hdd"></i>
                                                    <?php echo htmlspecialchars($resource['file_size']); ?>
                                                </span>
                                            <?php endif; ?>
                                            <span class="meta-item">
                                                <i class="fas fa-download"></i>
                                                <?php echo number_format($resource['download_count']); ?> downloads
                                            </span>
                                            <span class="meta-item">
                                                <span class="course-status <?php echo $resource['status']; ?>">
                                                    <?php echo ucfirst($resource['status']); ?>
                                                </span>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <?php if ($resource['description']): ?>
                                    <p class="resource-description"><?php echo htmlspecialchars($resource['description']); ?></p>
                                <?php endif; ?>

                                <div class="resource-actions">
                                    <a href="<?php echo htmlspecialchars($resource['google_drive_link']); ?>"
                                       target="_blank"
                                       class="btn-icon"
                                       title="View Resource">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    <a href="resources.php?action=edit&id=<?php echo $resource['id']; ?>"
                                       class="btn-icon edit"
                                       title="Edit Resource">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button onclick="deleteResource(<?php echo $resource['id']; ?>, '<?php echo htmlspecialchars($resource['title']); ?>')"
                                            class="btn-icon delete"
                                            title="Delete Resource">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Delete Form (Hidden) -->
    <form id="deleteForm" method="POST" style="display: none;">
        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
        <input type="hidden" name="action" value="delete">
        <input type="hidden" name="id" id="deleteId">
    </form>

    <script>
        // Course and subject data for JavaScript
        const coursesData = <?php echo json_encode($courses); ?>;
        const allSubjects = <?php echo json_encode($db->fetchAll("
            SELECT s.id, s.name, s.course_id, c.name as course_name
            FROM subjects s
            JOIN courses c ON s.course_id = c.id
            WHERE s.status = 'active' AND c.status = 'active'
            ORDER BY c.sort_order ASC, s.sort_order ASC, s.name ASC
        ")); ?>;

        // Load subjects when course is selected in form
        function loadSubjectsForForm() {
            const courseSelect = document.getElementById('course_select');
            const subjectSelect = document.getElementById('subject_id');
            const selectedCourseId = parseInt(courseSelect.value);

            // Clear existing options
            subjectSelect.innerHTML = '<option value="">Select a subject</option>';

            if (selectedCourseId) {
                // Filter subjects for selected course
                const filteredSubjects = allSubjects.filter(subject =>
                    subject.course_id === selectedCourseId
                );

                // Add filtered subjects to dropdown
                filteredSubjects.forEach(subject => {
                    const option = document.createElement('option');
                    option.value = subject.id;
                    option.textContent = subject.name;
                    subjectSelect.appendChild(option);
                });
            }
        }

        // Test Google Drive link
        function testGoogleDriveLink() {
            const linkInput = document.getElementById('google_drive_link');
            const link = linkInput.value.trim();

            if (!link) {
                alert('Please enter a Google Drive link first.');
                linkInput.focus();
                return;
            }

            // Basic validation
            const validPatterns = [
                /^https:\/\/drive\.google\.com\/file\/d\/[a-zA-Z0-9_-]+\/view/,
                /^https:\/\/drive\.google\.com\/open\?id=[a-zA-Z0-9_-]+/,
                /^https:\/\/docs\.google\.com\/document\/d\/[a-zA-Z0-9_-]+/
            ];

            const isValid = validPatterns.some(pattern => pattern.test(link));

            if (!isValid) {
                alert('This doesn\'t appear to be a valid Google Drive link format.\n\nExpected formats:\n- https://drive.google.com/file/d/FILE_ID/view\n- https://drive.google.com/open?id=FILE_ID\n- https://docs.google.com/document/d/FILE_ID/');
                return;
            }

            // Open link in new tab
            window.open(link, '_blank');
        }

        // Delete resource
        function deleteResource(id, title) {
            if (confirm(`Are you sure you want to delete the resource "${title}"?\n\nThis action cannot be undone.`)) {
                document.getElementById('deleteId').value = id;
                document.getElementById('deleteForm').submit();
            }
        }

        // Initialize form if editing
        document.addEventListener('DOMContentLoaded', function() {
            <?php if ($edit_resource): ?>
                loadSubjectsForForm();
            <?php endif; ?>
        });

        // Auto-dismiss alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 300);
            });
        }, 5000);
    </script>
</body>
</html>