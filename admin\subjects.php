<?php
/**
 * Subjects Management for SelexIQ Resources System
 */

require_once '../config/database.php';
requireAdminLogin();

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        $error = 'Invalid security token. Please try again.';
    } else {
        switch ($action) {
            case 'add':
                $course_id = (int)($_POST['course_id'] ?? 0);
                $name = sanitizeInput($_POST['name'] ?? '');
                $description = sanitizeInput($_POST['description'] ?? '');
                $icon = sanitizeInput($_POST['icon'] ?? 'fas fa-book-open');
                $sort_order = (int)($_POST['sort_order'] ?? 0);
                
                if (empty($name) || $course_id <= 0) {
                    $error = 'Subject name and course are required.';
                } else {
                    try {
                        $db->execute("
                            INSERT INTO subjects (course_id, name, description, icon, sort_order) 
                            VALUES (?, ?, ?, ?, ?)
                        ", [$course_id, $name, $description, $icon, $sort_order]);
                        $message = 'Subject added successfully!';
                    } catch (Exception $e) {
                        $error = 'Failed to add subject. Please try again.';
                    }
                }
                break;
                
            case 'edit':
                $id = (int)($_POST['id'] ?? 0);
                $course_id = (int)($_POST['course_id'] ?? 0);
                $name = sanitizeInput($_POST['name'] ?? '');
                $description = sanitizeInput($_POST['description'] ?? '');
                $icon = sanitizeInput($_POST['icon'] ?? 'fas fa-book-open');
                $sort_order = (int)($_POST['sort_order'] ?? 0);
                $status = sanitizeInput($_POST['status'] ?? 'active');
                
                if (empty($name) || $id <= 0 || $course_id <= 0) {
                    $error = 'Invalid subject data.';
                } else {
                    try {
                        $db->execute("
                            UPDATE subjects 
                            SET course_id = ?, name = ?, description = ?, icon = ?, sort_order = ?, status = ?
                            WHERE id = ?
                        ", [$course_id, $name, $description, $icon, $sort_order, $status, $id]);
                        $message = 'Subject updated successfully!';
                    } catch (Exception $e) {
                        $error = 'Failed to update subject. Please try again.';
                    }
                }
                break;
                
            case 'delete':
                $id = (int)($_POST['id'] ?? 0);
                if ($id > 0) {
                    try {
                        // Check if subject has resources
                        $resource_count = $db->fetch("SELECT COUNT(*) as count FROM resources WHERE subject_id = ?", [$id])['count'];
                        if ($resource_count > 0) {
                            $error = 'Cannot delete subject with existing resources. Please delete resources first.';
                        } else {
                            $db->execute("DELETE FROM subjects WHERE id = ?", [$id]);
                            $message = 'Subject deleted successfully!';
                        }
                    } catch (Exception $e) {
                        $error = 'Failed to delete subject. Please try again.';
                    }
                }
                break;
        }
    }
}

// Get filter parameters
$filter_course = (int)($_GET['course_id'] ?? 0);
$filter_status = sanitizeInput($_GET['status'] ?? '');

// Build WHERE clause for filtering
$where_conditions = [];
$where_params = [];

if ($filter_course > 0) {
    $where_conditions[] = "s.course_id = ?";
    $where_params[] = $filter_course;
}

if ($filter_status) {
    $where_conditions[] = "s.status = ?";
    $where_params[] = $filter_status;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// Get all subjects with course information
$subjects = $db->fetchAll("
    SELECT s.*, c.name as course_name, c.color as course_color,
           COUNT(r.id) as resource_count
    FROM subjects s 
    JOIN courses c ON s.course_id = c.id 
    LEFT JOIN resources r ON s.id = r.subject_id AND r.status = 'active'
    $where_clause
    GROUP BY s.id 
    ORDER BY c.sort_order ASC, c.name ASC, s.sort_order ASC, s.name ASC
", $where_params);

// Get all active courses for dropdown
$courses = $db->fetchAll("
    SELECT id, name, color 
    FROM courses 
    WHERE status = 'active' 
    ORDER BY sort_order ASC, name ASC
");

// Get subject for editing if requested
$edit_subject = null;
if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id'])) {
    $edit_id = (int)$_GET['id'];
    $edit_subject = $db->fetch("SELECT * FROM subjects WHERE id = ?", [$edit_id]);
}

$csrf_token = generateCSRFToken();
$page_title = "Subjects Management";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - SelexIQ Admin</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/forms.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="icon" href="../images/logo.png" type="image/png">
</head>
<body class="admin-dashboard">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-container">
            <nav class="admin-nav">
                <div class="admin-logo-container">
                    <img src="../images/logo.png" alt="SelexIQ Logo" class="admin-logo">
                </div>
                
                <div class="admin-menu">
                    <a href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="courses.php">
                        <i class="fas fa-graduation-cap"></i> Courses
                    </a>
                    <a href="subjects.php" class="active">
                        <i class="fas fa-book"></i> Subjects
                    </a>
                    <a href="resources.php">
                        <i class="fas fa-file-alt"></i> Resources
                    </a>
                    <a href="analytics.php">
                        <i class="fas fa-chart-bar"></i> Analytics
                    </a>
                    <a href="settings.php">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </div>
                
                <div class="admin-user">
                    <div class="user-info">
                        <div class="user-name"><?php echo htmlspecialchars($_SESSION['admin_username']); ?></div>
                        <div class="user-role">Administrator</div>
                    </div>
                    <a href="logout.php" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>
                    <i class="fas fa-book"></i>
                    Subjects Management
                </h1>
                <p>Organize subjects within your course categories</p>
            </div>

            <!-- Messages -->
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Filter Bar -->
            <div class="filter-bar">
                <form method="GET" class="filter-form">
                    <div class="filter-group">
                        <label for="filter_course">Filter by Course</label>
                        <select id="filter_course" name="course_id" onchange="this.form.submit()">
                            <option value="">All Courses</option>
                            <?php foreach ($courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>" 
                                        <?php echo $filter_course == $course['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($course['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="filter_status">Filter by Status</label>
                        <select id="filter_status" name="status" onchange="this.form.submit()">
                            <option value="">All Status</option>
                            <option value="active" <?php echo $filter_status === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo $filter_status === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                    </div>
                    
                    <div class="filter-actions">
                        <a href="subjects.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-times"></i> Clear Filters
                        </a>
                    </div>
                </form>
            </div>

            <!-- Add/Edit Subject Form -->
            <div class="dashboard-section">
                <h2>
                    <i class="fas fa-<?php echo $edit_subject ? 'edit' : 'plus'; ?>"></i>
                    <?php echo $edit_subject ? 'Edit Subject' : 'Add New Subject'; ?>
                </h2>
                
                <?php if (empty($courses)): ?>
                    <div class="no-data">
                        <i class="fas fa-graduation-cap"></i>
                        <h3>No Courses Available</h3>
                        <p>You need to create courses first before adding subjects.</p>
                        <a href="courses.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Course First
                        </a>
                    </div>
                <?php else: ?>
                    <form method="POST" class="admin-form">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" name="action" value="<?php echo $edit_subject ? 'edit' : 'add'; ?>">
                        <?php if ($edit_subject): ?>
                            <input type="hidden" name="id" value="<?php echo $edit_subject['id']; ?>">
                        <?php endif; ?>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="course_id">Course *</label>
                                <select id="course_id" name="course_id" required>
                                    <option value="">Select a course</option>
                                    <?php foreach ($courses as $course): ?>
                                        <option value="<?php echo $course['id']; ?>" 
                                                <?php echo ($edit_subject['course_id'] ?? $filter_course) == $course['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($course['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="name">Subject Name *</label>
                                <input type="text" 
                                       id="name" 
                                       name="name" 
                                       required 
                                       value="<?php echo htmlspecialchars($edit_subject['name'] ?? ''); ?>"
                                       placeholder="e.g., Mathematics">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="icon">Icon Class</label>
                                <input type="text" 
                                       id="icon" 
                                       name="icon" 
                                       value="<?php echo htmlspecialchars($edit_subject['icon'] ?? 'fas fa-book-open'); ?>"
                                       placeholder="e.g., fas fa-calculator">
                                <small>Use Font Awesome icon classes</small>
                            </div>
                            
                            <div class="form-group">
                                <label for="sort_order">Sort Order</label>
                                <input type="number" 
                                       id="sort_order" 
                                       name="sort_order" 
                                       value="<?php echo htmlspecialchars($edit_subject['sort_order'] ?? '0'); ?>"
                                       min="0">
                            </div>
                            
                            <?php if ($edit_subject): ?>
                            <div class="form-group">
                                <label for="status">Status</label>
                                <select id="status" name="status">
                                    <option value="active" <?php echo ($edit_subject['status'] ?? 'active') === 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?php echo ($edit_subject['status'] ?? 'active') === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                </select>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea id="description" 
                                      name="description" 
                                      rows="3"
                                      placeholder="Brief description of the subject"><?php echo htmlspecialchars($edit_subject['description'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                <?php echo $edit_subject ? 'Update Subject' : 'Add Subject'; ?>
                            </button>
                            
                            <?php if ($edit_subject): ?>
                                <a href="subjects.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                    Cancel
                                </a>
                            <?php endif; ?>
                        </div>
                    </form>
                <?php endif; ?>
            </div>

            <!-- Subjects List -->
            <div class="dashboard-section">
                <h2>
                    <i class="fas fa-list"></i>
                    Existing Subjects (<?php echo count($subjects); ?>)
                </h2>
                
                <?php if (empty($subjects)): ?>
                    <div class="no-data">
                        <i class="fas fa-book"></i>
                        <h3>No Subjects Yet</h3>
                        <p>Start by adding your first subject above.</p>
                    </div>
                <?php else: ?>
                    <div class="subjects-grid">
                        <?php foreach ($subjects as $subject): ?>
                            <div class="subject-item">
                                <div class="subject-header">
                                    <div class="subject-icon">
                                        <i class="<?php echo $subject['icon']; ?>"></i>
                                    </div>
                                    <div class="course-status <?php echo $subject['status']; ?>">
                                        <?php echo ucfirst($subject['status']); ?>
                                    </div>
                                </div>
                                
                                <div class="subject-course" style="color: <?php echo $subject['course_color']; ?>">
                                    <i class="fas fa-graduation-cap"></i>
                                    <?php echo htmlspecialchars($subject['course_name']); ?>
                                </div>
                                
                                <h4><?php echo htmlspecialchars($subject['name']); ?></h4>
                                <p><?php echo htmlspecialchars($subject['description'] ?: 'No description'); ?></p>
                                
                                <div class="course-stats">
                                    <span class="stat">
                                        <i class="fas fa-file-alt"></i>
                                        <?php echo $subject['resource_count']; ?> resources
                                    </span>
                                    <span class="stat">
                                        <i class="fas fa-sort-numeric-up"></i>
                                        Order: <?php echo $subject['sort_order']; ?>
                                    </span>
                                </div>
                                
                                <div class="subject-actions">
                                    <a href="subjects.php?action=edit&id=<?php echo $subject['id']; ?>" 
                                       class="btn-icon edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="resources.php?subject_id=<?php echo $subject['id']; ?>" 
                                       class="btn-icon">
                                        <i class="fas fa-file-alt"></i>
                                    </a>
                                    <?php if ($subject['resource_count'] == 0): ?>
                                        <button onclick="deleteSubject(<?php echo $subject['id']; ?>, '<?php echo htmlspecialchars($subject['name']); ?>')" 
                                                class="btn-icon delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Delete Form (Hidden) -->
    <form id="deleteForm" method="POST" style="display: none;">
        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
        <input type="hidden" name="action" value="delete">
        <input type="hidden" name="id" id="deleteId">
    </form>

    <script>
        function deleteSubject(id, name) {
            if (confirm(`Are you sure you want to delete the subject "${name}"?\n\nThis action cannot be undone.`)) {
                document.getElementById('deleteId').value = id;
                document.getElementById('deleteForm').submit();
            }
        }
        
        // Auto-dismiss alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 300);
            });
        }, 5000);
    </script>
</body>
</html>
